<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ApprovablePlanable extends Model
{
    protected $table = 'approvable_planable';

    protected $fillable = ['planable_id', 'approvable_id', 'request_type', 'required', 'flow', 'show_data', 'num_days'];

    public function planable()
    {
        return $this->belongsTo(Planable::class, 'planable_id');
    }

    public function approvable()
    {
        return $this->belongsTo(Approvable::class, 'approvable_id');
    }
    public function divisionTypeApprovable()
    {
        return $this->belongsTo(DivisionType::class, 'approvable_id');
    }
}
