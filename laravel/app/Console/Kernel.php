<?php

namespace App\Console;

use App\Services\SchedulerDynamicSetup;
use Exception;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\FavouriteListActionCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        $schedule->command('cache:prune-stale-tags')
            ->timezone(config('app.timezone'))
            ->hourly();

        if (app()->runningInConsole() && app()->environment('local')) {
            // Only run this during local testing
            // $schedule->command('app:approval-time-command')->everyMinute();
        }

        $lock = Cache::lock('dynamic-schedule-setup', 60);
        if ($lock->get()) {
            try {
                new SchedulerDynamicSetup($schedule);
            } catch (Exception $e) {
                Log::error('Failed to set up alert schedules', [
                    'error' => $e->getMessage()
                ]);
            } finally {
                $lock->release();
            }
        }


        if (!str(config('app.url'))->contains("localhost")) {
            $schedule->command('backup:clean')
                ->runInBackground()
                ->daily()
                ->timezone(config('app.timezone'))
                ->at('12:00')
                ->appendOutputTo('storage/logs/backup.log');

            $schedule->command('backup:run')
                ->runInBackground()
                ->daily()
                ->timezone(config('app.timezone'))
                ->at('12:20')
                ->appendOutputTo('storage/logs/backup.log');
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');
        require base_path('routes/console.php');
    }
}
