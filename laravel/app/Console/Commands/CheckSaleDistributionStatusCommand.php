<?php

namespace App\Console\Commands;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Sales\Ceiling\DistributionService;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckSaleDistributionStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'distribution:check-sale-status 
                            {sale_id : The ID of the sale to check}
                            {--detailed : Show detailed analysis and criteria}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check whether a specific sale has been distributed or not, with detailed analysis of distribution criteria';

    /**
     * Distribution service instance
     */
    private DistributionService $distributionService;

    /**
     * Create a new command instance.
     */
    public function __construct(DistributionService $distributionService)
    {
        parent::__construct();
        $this->distributionService = $distributionService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $saleId = $this->argument('sale_id');
            $detailed = $this->option('detailed');

            // Validate sale ID
            if (!is_numeric($saleId) || $saleId <= 0) {
                $this->error('Sale ID must be a positive integer');
                return Command::FAILURE;
            }

            // Find the sale
            $sale = Sale::with(['product', 'distributor', 'mappings', 'details'])->find($saleId);
            
            if (!$sale) {
                $this->error("Sale with ID {$saleId} not found");
                return Command::FAILURE;
            }

            $this->printHeader($sale);
            
            // Check current distribution status
            $currentStatus = $this->getCurrentDistributionStatus($sale);
            $this->printCurrentStatus($currentStatus);

            // Analyze distribution eligibility
            $eligibilityAnalysis = $this->analyzeDistributionEligibility($sale);
            $this->printEligibilityAnalysis($eligibilityAnalysis);

            // Show detailed analysis if requested
            if ($detailed) {
                $this->showDetailedAnalysis($sale, $eligibilityAnalysis);
            }

            // Print final conclusion
            $this->printConclusion($currentStatus, $eligibilityAnalysis);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("ERROR: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Get current distribution status of the sale
     */
    private function getCurrentDistributionStatus(Sale $sale): array
    {
        $status = [
            'ceiling_value' => $sale->ceiling,
            'ceiling_name' => $this->getCeilingName($sale->ceiling),
            'is_distributed' => false,
            'distribution_type' => null,
            'related_sales' => []
        ];

        switch ($sale->ceiling) {
            case Ceiling::BELOW->value:
                $status['is_distributed'] = false;
                $status['description'] = 'Sale is in normal state (below ceiling limit)';
                break;
                
            case Ceiling::ABOVE->value:
                $status['is_distributed'] = false;
                $status['description'] = 'Sale exceeded ceiling and was marked as original (likely replaced by distributed sales)';
                
                // Check if there are related distributed sales
                $relatedSales = $this->findRelatedDistributedSales($sale);
                if (!empty($relatedSales)) {
                    $status['is_distributed'] = true;
                    $status['related_sales'] = $relatedSales;
                    $status['description'] = 'Sale exceeded ceiling and has been distributed (original sale marked as ABOVE, distributed sales created)';
                }
                break;
                
            case Ceiling::DISTRIBUTED->value:
                $status['is_distributed'] = true;
                $status['description'] = 'Sale is a distributed sale created from ceiling violation';
                
                // Try to find the original sale(s) this was created from
                $originalSales = $this->findOriginalSalesForDistributed($sale);
                if (!empty($originalSales)) {
                    $status['related_sales'] = $originalSales;
                }
                break;
                
            default:
                $status['description'] = 'Unknown ceiling status';
        }

        return $status;
    }

    /**
     * Find related distributed sales for an ABOVE sale
     */
    private function findRelatedDistributedSales(Sale $sale): array
    {
        // Look for distributed sales with same product, distributor, and date
        // that might have been created from this original sale
        $distributedSales = Sale::where('ceiling', Ceiling::DISTRIBUTED->value)
            ->where('product_id', $sale->product_id)
            ->where('distributor_id', $sale->distributor_id)
            ->where('date', $sale->date)
            ->whereNotNull('sale_ids')
            ->get();

        $relatedSales = [];
        foreach ($distributedSales as $distributedSale) {
            // Check if this sale's ID is in the sale_ids field
            $saleIds = explode(',', $distributedSale->sale_ids ?? '');
            if (in_array($sale->id, $saleIds)) {
                $relatedSales[] = [
                    'id' => $distributedSale->id,
                    'quantity' => $distributedSale->quantity,
                    'value' => $distributedSale->value,
                    'bonus' => $distributedSale->bonus,
                    'type' => 'distributed'
                ];
            }
        }

        return $relatedSales;
    }

    /**
     * Find original sales for a distributed sale
     */
    private function findOriginalSalesForDistributed(Sale $sale): array
    {
        $originalSales = [];
        
        if (!empty($sale->sale_ids)) {
            $saleIds = explode(',', $sale->sale_ids);
            $originalSalesData = Sale::whereIn('id', $saleIds)->get();
            
            foreach ($originalSalesData as $originalSale) {
                $originalSales[] = [
                    'id' => $originalSale->id,
                    'quantity' => $originalSale->quantity,
                    'value' => $originalSale->value,
                    'bonus' => $originalSale->bonus,
                    'ceiling' => $originalSale->ceiling,
                    'type' => 'original'
                ];
            }
        }

        return $originalSales;
    }

    /**
     * Analyze if the sale meets distribution eligibility criteria
     */
    private function analyzeDistributionEligibility(Sale $sale): array
    {
        $analysis = [
            'eligible' => false,
            'reasons' => [],
            'criteria_checks' => []
        ];

        // Check 1: Sale must have ceiling = BELOW to be eligible for distribution
        $analysis['criteria_checks']['ceiling_below'] = $sale->ceiling === Ceiling::BELOW->value;
        if (!$analysis['criteria_checks']['ceiling_below']) {
            $analysis['reasons'][] = "Sale ceiling is '{$this->getCeilingName($sale->ceiling)}', must be 'BELOW' to be eligible";
        }

        // Check 2: Sale must have mappings
        $analysis['criteria_checks']['has_mappings'] = $sale->mappings->isNotEmpty();
        if (!$analysis['criteria_checks']['has_mappings']) {
            $analysis['reasons'][] = "Sale has no mappings attached";
        }

        // Check 3: Mappings must not be exceptions
        $hasNonExceptionMappings = false;
        if ($sale->mappings->isNotEmpty()) {
            $hasNonExceptionMappings = $sale->mappings->where('exception', false)->isNotEmpty();
        }
        $analysis['criteria_checks']['non_exception_mappings'] = $hasNonExceptionMappings;
        if (!$hasNonExceptionMappings) {
            $analysis['reasons'][] = "All mappings are marked as exceptions";
        }

        // Check 4: Product ceiling requirements (updated for STORES distribution type)
        $distributionType = $this->getDistributionType($sale);
        $productCeiling = $this->getActiveProductCeiling($sale);

        // For STORES distribution type, product ceiling is optional (LEFT JOIN logic)
        if ($distributionType === DistributionType::STORES) {
            $analysis['criteria_checks']['has_product_ceiling'] = true; // Always pass for STORES
            $analysis['criteria_checks']['exceeds_ceiling'] = true; // Always pass for STORES

            if ($productCeiling) {
                // If ceiling exists, check if it's exceeded
                $exceedsCeiling = abs($sale->quantity) > $productCeiling->units;
                $analysis['product_ceiling'] = [
                    'limit' => $productCeiling->units,
                    'negative_limit' => $productCeiling->negative_units,
                    'sale_quantity' => $sale->quantity,
                    'exceeds_limit' => $exceedsCeiling,
                    'note' => 'STORES distribution allows processing regardless of ceiling'
                ];
            } else {
                $analysis['product_ceiling'] = [
                    'limit' => null,
                    'negative_limit' => null,
                    'sale_quantity' => $sale->quantity,
                    'exceeds_limit' => true,
                    'note' => 'No ceiling defined - STORES distribution processes all sales'
                ];
            }
        } else {
            // For other distribution types, product ceiling is required
            $analysis['criteria_checks']['has_product_ceiling'] = !is_null($productCeiling);
            if (!$analysis['criteria_checks']['has_product_ceiling']) {
                $analysis['reasons'][] = "Product has no active ceiling limits";
            }

            // Check 5: Sale quantity must exceed ceiling limit (if ceiling exists)
            $exceedsCeiling = false;
            if ($productCeiling) {
                $exceedsCeiling = abs($sale->quantity) > $productCeiling->units;
                $analysis['criteria_checks']['exceeds_ceiling'] = $exceedsCeiling;
                $analysis['product_ceiling'] = [
                    'limit' => $productCeiling->units,
                    'negative_limit' => $productCeiling->negative_units,
                    'sale_quantity' => $sale->quantity,
                    'exceeds_limit' => $exceedsCeiling
                ];

                if (!$exceedsCeiling) {
                    $analysis['reasons'][] = "Sale quantity ({$sale->quantity}) does not exceed ceiling limit ({$productCeiling->units})";
                }
            }
        }

        // Check 6: Mapping must have valid distribution type
        $distributionType = $this->getDistributionType($sale);
        $analysis['criteria_checks']['valid_distribution_type'] = !is_null($distributionType);
        $analysis['distribution_type'] = $distributionType;
        $analysis['distribution_eligibility'] = $this->analyzeDistributionTypeEligibility($sale);

        if (!$distributionType) {
            $analysis['reasons'][] = "Cannot determine valid distribution type from mappings";
        }

        // Overall eligibility
        $analysis['eligible'] = empty($analysis['reasons']);

        return $analysis;
    }

    /**
     * Get active product ceiling for the sale
     */
    private function getActiveProductCeiling(Sale $sale)
    {
        return DB::table('product_ceilings')
            ->where('product_id', $sale->product_id)
            ->where('from_date', '<=', now())
            ->where(function($q) {
                $q->where('to_date', '>', now())
                  ->orWhere('to_date', null);
            })
            ->first();
    }

    /**
     * Determine distribution type from sale mappings
     * Updated to align with new PRIVATE_PHARMACY business rules
     */
    private function getDistributionType(Sale $sale): ?DistributionType
    {
        if ($sale->mappings->isEmpty()) {
            return null;
        }

        // Check the unified_pharmacy_type_id from the first mapping
        $mapping = $sale->mappings->first();

        if (!$mapping->unified_pharmacy_type_id) {
            // Null type is eligible for PRIVATE_PHARMACY distribution
            return DistributionType::PRIVATE_PHARMACY;
        }

        $distributionType = DistributionType::fromValue($mapping->unified_pharmacy_type_id);

        // Apply new business rules: exclude STORES and LOCAL_CHAINS from PRIVATE_PHARMACY processing
        if ($distributionType === DistributionType::STORES || $distributionType === DistributionType::LOCAL_CHAINS) {
            // These types are only eligible for their specific distribution types
            return $distributionType;
        }

        // All other types (including PRIVATE_PHARMACY and UNKNOWN) are eligible for PRIVATE_PHARMACY distribution
        return DistributionType::PRIVATE_PHARMACY;
    }

    /**
     * Analyze distribution type eligibility based on new business rules
     */
    private function analyzeDistributionTypeEligibility(Sale $sale): array
    {
        $analysis = [
            'eligible_for_private_pharmacy' => false,
            'eligible_for_stores' => false,
            'eligible_for_local_chains' => false,
            'actual_type' => null,
            'explanation' => ''
        ];

        if ($sale->mappings->isEmpty()) {
            $analysis['explanation'] = 'No mappings found - cannot determine distribution type eligibility';
            return $analysis;
        }

        $mapping = $sale->mappings->first();
        $actualTypeId = $mapping->unified_pharmacy_type_id;

        if ($actualTypeId === null) {
            $analysis['eligible_for_private_pharmacy'] = true;
            $analysis['actual_type'] = 'NULL';
            $analysis['explanation'] = 'NULL distribution type - eligible for PRIVATE_PHARMACY processing';
        } else {
            $actualType = DistributionType::fromValue($actualTypeId);
            $analysis['actual_type'] = $actualType->getName();

            switch ($actualType) {
                case DistributionType::PRIVATE_PHARMACY:
                    $analysis['eligible_for_private_pharmacy'] = true;
                    $analysis['explanation'] = 'PRIVATE_PHARMACY type - eligible for PRIVATE_PHARMACY processing';
                    break;

                case DistributionType::STORES:
                    $analysis['eligible_for_stores'] = true;
                    $analysis['explanation'] = 'STORES type - only eligible for STORES processing (excluded from PRIVATE_PHARMACY). Note: STORES distribution allows processing even without product ceiling limits.';
                    break;

                case DistributionType::LOCAL_CHAINS:
                    $analysis['eligible_for_local_chains'] = true;
                    $analysis['explanation'] = 'LOCAL_CHAINS type - only eligible for LOCAL_CHAINS processing (excluded from PRIVATE_PHARMACY)';
                    break;

                default:
                    $analysis['eligible_for_private_pharmacy'] = true;
                    $analysis['explanation'] = "Other distribution type ({$actualType->getName()}) - eligible for PRIVATE_PHARMACY processing (new business rule)";
                    break;
            }
        }

        return $analysis;
    }

    /**
     * Get ceiling name from value
     */
    private function getCeilingName(string $value): string
    {
        return match($value) {
            Ceiling::BELOW->value => 'BELOW',
            Ceiling::ABOVE->value => 'ABOVE',
            Ceiling::DISTRIBUTED->value => 'DISTRIBUTED',
            default => 'UNKNOWN'
        };
    }

    /**
     * Print command header with sale information
     */
    private function printHeader(Sale $sale): void
    {
        $this->info("=== SALE DISTRIBUTION STATUS CHECK ===");
        $this->line("Sale ID: {$sale->id}");
        $this->line("Product: {$sale->product->name} (ID: {$sale->product_id})");
        $this->line("Distributor: " . ($sale->distributor->name ?? 'N/A') . " (ID: {$sale->distributor_id})");
        $this->line("Date: {$sale->date}");
        $this->line("Quantity: {$sale->quantity}");
        $this->line("Value: {$sale->value}");
        $this->line("Bonus: {$sale->bonus}");
        $this->line("Region: {$sale->region}");
        $this->line("");
    }

    /**
     * Print current distribution status
     */
    private function printCurrentStatus(array $status): void
    {
        $this->info("=== CURRENT DISTRIBUTION STATUS ===");

        $statusIcon = $status['is_distributed'] ? '✅' : '❌';
        $this->line("Distributed: {$statusIcon} " . ($status['is_distributed'] ? 'YES' : 'NO'));
        $this->line("Ceiling Status: {$status['ceiling_name']} ({$status['ceiling_value']})");
        $this->line("Description: {$status['description']}");

        if (!empty($status['related_sales'])) {
            $this->line("\nRelated Sales:");
            foreach ($status['related_sales'] as $relatedSale) {
                $this->line("  - Sale ID {$relatedSale['id']} ({$relatedSale['type']}): Qty={$relatedSale['quantity']}, Value={$relatedSale['value']}, Bonus={$relatedSale['bonus']}");
            }
        }

        $this->line("");
    }

    /**
     * Print distribution eligibility analysis
     */
    private function printEligibilityAnalysis(array $analysis): void
    {
        $this->info("=== DISTRIBUTION ELIGIBILITY ANALYSIS ===");

        $eligibleIcon = $analysis['eligible'] ? '✅' : '❌';
        $this->line("Eligible for Distribution: {$eligibleIcon} " . ($analysis['eligible'] ? 'YES' : 'NO'));

        if (!$analysis['eligible']) {
            $this->line("\nReasons why sale is NOT eligible:");
            foreach ($analysis['reasons'] as $reason) {
                $this->line("  ❌ {$reason}");
            }
        }

        $this->line("\nCriteria Checks:");
        foreach ($analysis['criteria_checks'] as $check => $passed) {
            $icon = $passed ? '✅' : '❌';
            $checkName = str_replace('_', ' ', ucwords($check, '_'));
            $this->line("  {$icon} {$checkName}");
        }

        if (isset($analysis['product_ceiling'])) {
            $ceiling = $analysis['product_ceiling'];
            $this->line("\nProduct Ceiling Details:");
            $this->line("  Ceiling Limit: " . ($ceiling['limit'] ?? 'N/A'));
            $this->line("  Negative Limit: " . ($ceiling['negative_limit'] ?? 'N/A'));
            $this->line("  Sale Quantity: {$ceiling['sale_quantity']}");
            $this->line("  Exceeds Limit: " . ($ceiling['exceeds_limit'] ? 'YES' : 'NO'));
            if (isset($ceiling['note'])) {
                $this->line("  Note: {$ceiling['note']}");
            }
        }

        if ($analysis['distribution_type']) {
            $this->line("\nDistribution Type: {$analysis['distribution_type']->getName()} ({$analysis['distribution_type']->value})");
        }

        if (isset($analysis['distribution_eligibility'])) {
            $eligibility = $analysis['distribution_eligibility'];
            $this->line("\nDistribution Type Eligibility (New Business Rules):");
            $this->line("  Actual Type: {$eligibility['actual_type']}");
            $this->line("  Eligible for PRIVATE_PHARMACY: " . ($eligibility['eligible_for_private_pharmacy'] ? '✅ YES' : '❌ NO'));
            $this->line("  Eligible for STORES: " . ($eligibility['eligible_for_stores'] ? '✅ YES' : '❌ NO'));
            $this->line("  Eligible for LOCAL_CHAINS: " . ($eligibility['eligible_for_local_chains'] ? '✅ YES' : '❌ NO'));
            $this->line("  Explanation: {$eligibility['explanation']}");
        }

        $this->line("");
    }

    /**
     * Show detailed analysis
     */
    private function showDetailedAnalysis(Sale $sale, array $eligibilityAnalysis): void
    {
        $this->info("=== DETAILED ANALYSIS ===");

        // Show mapping details
        $this->line("--- Mapping Details ---");
        if ($sale->mappings->isNotEmpty()) {
            foreach ($sale->mappings as $mapping) {
                $this->line("Mapping ID {$mapping->id}: {$mapping->name}");
                $this->line("  Unified Pharmacy Type: " . ($mapping->unified_pharmacy_type_id ?? 'NULL'));
                $this->line("  Exception: " . ($mapping->exception ? 'YES' : 'NO'));
                $this->line("  Line ID: " . ($mapping->line_id ?? 'NULL'));
            }

            // Show distribution type eligibility analysis
            $eligibility = $this->analyzeDistributionTypeEligibility($sale);
            $this->line("\n--- Distribution Type Eligibility Analysis ---");
            $this->line("Actual Type: {$eligibility['actual_type']}");
            $this->line("Eligible for PRIVATE_PHARMACY: " . ($eligibility['eligible_for_private_pharmacy'] ? '✅ YES' : '❌ NO'));
            $this->line("Eligible for STORES: " . ($eligibility['eligible_for_stores'] ? '✅ YES' : '❌ NO'));
            $this->line("Eligible for LOCAL_CHAINS: " . ($eligibility['eligible_for_local_chains'] ? '✅ YES' : '❌ NO'));
            $this->line("Explanation: {$eligibility['explanation']}");
        } else {
            $this->line("No mappings found");
        }

        // Show sales details breakdown
        $this->line("\n--- Sales Details Breakdown ---");
        if ($sale->details->isNotEmpty()) {
            $totalDetailQuantity = $sale->details->sum('quantity');
            $totalDetailValue = $sale->details->sum('value');
            $totalDetailBonus = $sale->details->sum('bonus');

            $this->line("Total Details: {$sale->details->count()}");
            $this->line("Detail Quantities Sum: {$totalDetailQuantity}");
            $this->line("Detail Values Sum: {$totalDetailValue}");
            $this->line("Detail Bonuses Sum: {$totalDetailBonus}");

            $quantityMatch = abs($sale->quantity - $totalDetailQuantity) < 0.001;
            $valueMatch = abs($sale->value - $totalDetailValue) < 0.001;
            $bonusMatch = abs($sale->bonus - $totalDetailBonus) < 0.001;

            $this->line("Quantity Match: " . ($quantityMatch ? '✅' : '❌'));
            $this->line("Value Match: " . ($valueMatch ? '✅' : '❌'));
            $this->line("Bonus Match: " . ($bonusMatch ? '✅' : '❌'));
        } else {
            $this->line("No sales details found");
        }

        // Show distribution service query simulation
        $this->line("\n--- Distribution Service Query Simulation ---");
        $this->simulateDistributionQuery($sale);

        $this->line("");
    }

    /**
     * Simulate what the distribution service would find for this sale
     * Uses updated business rules that exclude only STORES and LOCAL_CHAINS from PRIVATE_PHARMACY processing
     */
    private function simulateDistributionQuery(Sale $sale): void
    {
        try {
            $distributionType = $this->getDistributionType($sale);

            if (!$distributionType) {
                $this->line("Cannot simulate - no valid distribution type");
                return;
            }

            // Query for ceiling sales using the same logic as DistributionService
            // This now uses the updated business rules for PRIVATE_PHARMACY distribution
            $date = $sale->date;
            $productIds = [$sale->product_id];
            $distributorIds = [$sale->distributor_id];

            $ceilingSales = $this->distributionService->queryCeilingSales(
                $distributionType,
                $date,
                $date,
                $productIds,
                $distributorIds
            );

            $this->line("Distribution Service Query Results (using updated business rules):");
            $this->line("  Distribution Type: {$distributionType->getName()}");

            if ($distributionType === DistributionType::STORES) {
                $this->line("  Note: STORES distribution uses LEFT JOIN for product_ceilings (allows sales without ceiling limits)");
            }

            $this->line("  Found {$ceilingSales->count()} ceiling sales for this criteria");

            foreach ($ceilingSales as $ceilingSale) {
                $saleIds = explode(',', $ceilingSale->sale_ids);
                $containsThisSale = in_array($sale->id, $saleIds);

                $this->line("  Ceiling Sale: Product {$ceilingSale->id}, Mapping {$ceilingSale->mapping_id}");
                $this->line("    Total Units: {$ceilingSale->number_of_units}");
                $this->line("    Limit: {$ceilingSale->limit}");
                $this->line("    Sale IDs: {$ceilingSale->sale_ids}");
                $this->line("    Contains This Sale: " . ($containsThisSale ? 'YES' : 'NO'));
            }

        } catch (\Exception $e) {
            $this->line("Error simulating distribution query: " . $e->getMessage());
        }
    }

    /**
     * Print final conclusion
     */
    private function printConclusion(array $currentStatus, array $eligibilityAnalysis): void
    {
        $this->info("=== CONCLUSION ===");

        if ($currentStatus['is_distributed']) {
            $this->info("🎯 This sale HAS BEEN DISTRIBUTED");

            if ($currentStatus['ceiling_value'] === Ceiling::DISTRIBUTED->value) {
                $this->line("   → This is a distributed sale created from ceiling violation");
            } else {
                $this->line("   → This is an original sale that was distributed (marked as ABOVE)");
            }
        } else {
            $this->info("📋 This sale HAS NOT BEEN DISTRIBUTED");

            if ($eligibilityAnalysis['eligible']) {
                $this->warn("   ⚠️  Sale meets all criteria for distribution but hasn't been processed yet");
            } else {
                $this->line("   → Sale does not meet distribution criteria");
            }
        }

        $this->line("");
        $this->line("For more details, run with --detailed flag");
    }
}
