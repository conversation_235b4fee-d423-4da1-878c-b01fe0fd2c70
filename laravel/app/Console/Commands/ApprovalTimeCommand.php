<?php

namespace App\Console\Commands;

use App\Services\ApprovalTimeService;
use Illuminate\Console\Command;

class ApprovalTimeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:approval-time-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Approval Time Command';

    /**
     * Execute the console command.
     */


    protected $approvalTime;

    public function __construct(ApprovalTimeService $approvalTime)
    {
        parent::__construct();
        $this->approvalTime = $approvalTime;
    }
    public function handle()
    {
        $this->info('Handling Approval Time...');
        $this->approvalTime->handleApprovalTime();
        $this->info('Approval Time handled.');
    }
}
