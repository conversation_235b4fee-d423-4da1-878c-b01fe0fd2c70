<?php

namespace App\Services;

use App\ApprovablePlanable;
use App\DivisionType;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\PV\PV;
use App\Position;
use App\Vacation;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class ApprovalTimeService
{
    public function handleApprovalTime()
    {
        $approvalSetting = ApprovalSetting::where('key', 'vacation_approval_center_flow')->value('value');

        $rawData = $this->allApprovables();


        $users = $this->getUsersData($rawData);

        return $this->checkApprovals($users, $approvalSetting);
    }

    private function allApprovables()
    {
        return ApprovablePlanable::with([
            'approvable.approvable' => function ($morphTo) {
                $morphTo->morphWith([
                    \App\Position::class => ['users:id,fullname'],
                    \App\DivisionType::class => ['divisions.users:id,fullname'],
                ]);
            }
        ])
            ->whereIn('request_type', [Vacation::class, PV::class])
            ->where('num_days', '>', 0)
            ->get();
    }

    private function getUsersData($rawData)
    {
        $userDays = collect();

        foreach ($rawData as $item) {
            $approvable = $item->approvable?->approvable;

            if (!$approvable) continue;

            $users = $approvable instanceof \App\Position
                ? ($approvable->users ?? collect())
                : ($approvable instanceof \App\DivisionType
                    ? $approvable->divisions->flatMap(fn($d) => $d->users)
                    : collect());

            foreach ($users as $user) {
                $existing = $userDays->get($user->id);

                if (!$existing) {
                    // Clone the user and attach a custom property (avoid overriding core model attributes)
                    $user->days = $item->num_days;
                    $userDays->put($user->id, $user);
                } else {
                    $existing->days += $item->num_days;
                }
            }
        }

        return $userDays->sortByDesc('days')->values()->take(1);
    }

    private function checkApprovals($users, $approvalSetting)
    {
        $today = now()->toDateString(); // e.g. "2025-06-19"
        foreach ($users as $user) {
            $lines = $user->lines;
            $usersObjIds = $this->getUsersObjects($user, $lines);
            $vacations =  Vacation::whereIntegerInRaw('user_id', $usersObjIds)
                ->whereHas('details', function ($q) {
                    $q->whereNull('approval');
                })->get()->filter(function ($vacation) use ($today, $user) {

                    return $vacation->created_at
                        ->copy()
                        ->addDays($user->days)
                        ->toDateString() <= $today;
                })
                ->values(); // reset keys if needed


            $vacations = $this->checkApproval($vacations, $user, $lines, $approvalSetting);

            foreach ($vacations as $vacation) {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $vacation->details->id,
                    'user_id' => $user->id,
                    'approval' => 1,
                    'description' => 'Auto Approved'
                ]);
            }
            Log::info($vacations);
        }
    }
    private function getUsersObjects($user, $lines)
    {
        $usersObj = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($lines as $line) {
            foreach ($types as $type) {
                $usersObj = $usersObj->merge($user->planableUsers($line->id, Vacation::class, $type));
            }
        }
        return $usersObj->unique('id')->values()->pluck('id');
    }

    private function checkApproval($vacations, $user, $lines, $approvalSetting)
    {
        $required = 0;
        $dataFlow = '';
        $scanLevel = 1;
        $vacations = $vacations->filter(
            function (Vacation $vacation) use ($lines, $approvalSetting, $scanLevel, $user, &$required, $dataFlow) {
                $from = Carbon::parse($vacation->from_date)->startOfMonth();
                $to = Carbon::parse($vacation->to_date)->endOfMonth();
                if ($approvalSetting == 'Yes' && isNullable($vacation->details?->approval)) {
                    $approvalData = $user->userApprovals($from, $to);
                    $linesAapprovables = $approvalData['linesAapprovables'];
                    $approvablesCountOnThisShit = $vacation->details?->approvalFlows()->count();
                    $data = $user->approvalWidget($vacation, $user, Vacation::class, $from, $to, $lines, $linesAapprovables);
                    $dataFlow = $data['linesDataFlow'];
                    $currentFlow = $dataFlow?->flow;
                    $required = $dataFlow?->required;
                    $showData = $dataFlow?->show_data;
                    $vacantCount = $data['vacantCount'];
                    $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                    return $haveToApprove || $required || $showData;
                }
            }
        );
        return $vacations->values();
    }
}
