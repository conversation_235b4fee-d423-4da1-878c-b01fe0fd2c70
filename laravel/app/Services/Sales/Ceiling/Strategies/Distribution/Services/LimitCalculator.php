<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;

class LimitCalculator implements LimitCalculatorInterface
{
    /**
     * Calculate the limit for a ceiling sale
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateLimit($ceilingSale): float
    {
        if ($ceilingSale->number_of_units > 0) {
            return $ceilingSale->limit ?? 0;
        }

        if ($ceilingSale->number_of_units < 0) {
            return $ceilingSale->negative_limit ?? 0;
        }

        return 0;
    }

    /**
     * Check if the ceiling sale exceeds the limit
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return bool
     */
    public function exceedsLimit($ceilingSale): bool
    {
        // If no ceiling limits exist (null values), consider it as exceeding for processing
        if ($ceilingSale->number_of_units > 0 && $ceilingSale->limit === null) {
            return true;
        }

        if ($ceilingSale->number_of_units < 0 && $ceilingSale->negative_limit === null) {
            return true;
        }

        $limit = $this->calculateLimit($ceilingSale);

        if ($ceilingSale->number_of_units > 0) {
            return $ceilingSale->number_of_units > $limit;
        }

        if ($ceilingSale->number_of_units < 0) {
            return $ceilingSale->number_of_units < $limit;
        }

        return false;
    }
}
