<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\LineDivision;
use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\SalesService;
use App\Services\Structure\LineDivisionService;
use Illuminate\Support\Collection;

/**
 * Hierarchical Chain Distribution Algorithm for Local Chain Pharmacies
 *
 * This algorithm implements a sophisticated distribution strategy specifically designed
 * for local pharmacy chains. It uses a hierarchical 100-distribution for current district approach
 * that reflects the typical organizational structure of local pharmacy chains.
 *
 */
class HierarchicalChainDistributionAlgorithm implements ExcessDistributorInterface
{

    public function __construct(
        private readonly SalesService      $salesService,
        private readonly SaleDetailFactory $saleDetailFactory,
        private readonly HierarchicalDistributionService $hierarchicalDistributionService,
    )
    {

    }

    /**
     * Distribute excess sale using simple 100% distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn, ?Sale $originalSale = null): bool
    {
        $divisionsBricks = $this->getDistributionRatios($sale, $originalSale, $salesContributionBaseOn);

        $details = $this->saleDetailFactory->createDetailsFromRatios($sale, $divisionsBricks->toArray());

        return $this->saleDetailFactory->insertDetails($details);
    }

    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        $limit = 0;
        if ($ceilingSale->number_of_units > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($ceilingSale->number_of_units < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        return $ceilingSale->number_of_units - $limit;
    }


    /**
     * Get secondary distribution ratios (10% portion)
     *
     * @param Sale $sale
     * @param Sale|null $originalSale
     * @param array $salesContributionBaseOn
     * @return Collection
     */
    private function getDistributionRatios(Sale $sale,?Sale $originalSale, array $salesContributionBaseOn): Collection
    {
        if (!$originalSale) {
            return collect();
        }

        $divisionIds = $this->hierarchicalDistributionService->getDeepestDescendantIds($originalSale);

        return $this->salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn,
                $divisionIds
            );
    }

}
