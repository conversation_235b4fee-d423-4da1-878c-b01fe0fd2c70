<?php

namespace App\Services;

use App\ClassFrequency;
use App\Doctor;
use App\DoctorFrequency;
use App\Exceptions\CrmException;
use App\Models\OtherSetting;
use App\Models\SpecialityClassFrequency;
use App\SpecialityFrequency;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class FrequencyTypeValidationService
{
    // Cache TTL in seconds (1 hour)
    const CACHE_TTL = 3600;

    // Static caches for request-level caching
    private static $doctorCache = [];
    private static $settingCache = null;
    private static $frequencyCache = [];

    /**
     * Get frequency for a specific doctor with optimized caching and queries
     */
    public function classType($account_id, $doctor_id, $line_id, $date)
    {
        $fromMonth = (int) Carbon::parse($date)->format('m');
        $fromYear = (int) Carbon::parse($date)->format('Y');
        
        $doctor = $this->getCachedDoctor($doctor_id);

        $frequencyType = $this->getCachedFrequencyType();

        $frequency = match ($frequencyType) {
            1 => $this->getClassFrequency($doctor->class_id, $line_id, $fromMonth, $fromYear),
            2 => $this->getDoctorFrequency($doctor?->id, $account_id, $line_id, $fromMonth, $fromYear),
            3 => $this->getSpecialityFrequency($doctor->speciality_id, $line_id, $fromMonth, $fromYear),
            default => $this->getSpecialityClassFrequency($doctor->speciality_id, $doctor->class_id, $line_id, $fromMonth, $fromYear)
        };

        if($account_id == 3862)
            Log::info($frequency);

        // Cache the result for this request
        return $frequency;
    }

    /**
     * Get cached doctor data
     */
    private function getCachedDoctor($doctor_id)
    {
        if (!isset(self::$doctorCache[$doctor_id])) {
            self::$doctorCache[$doctor_id] = Doctor::select('id', 'class_id', 'speciality_id')
                ->where('id', $doctor_id)
                ->first();
        }

        return self::$doctorCache[$doctor_id];
    }

    /**
     * Get cached frequency type setting
     */
    private function getCachedFrequencyType()
    {
        return (int) OtherSetting::value('value') ?? 4;
    }

    /**
     * Optimized method to get class frequency using computed columns
     */
    private function getClassFrequency($class_id, $line_id, $month, $year)
    {
        return ClassFrequency::where('class_id', $class_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Optimized method to get doctor frequency using computed columns
     */
    private function getDoctorFrequency($doctor_id, $account_id, $line_id, $month, $year)
    {
        return DoctorFrequency::where('doctor_id', $doctor_id)
            ->where('account_id', $account_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Optimized method to get speciality frequency using computed columns
     */
    private function getSpecialityFrequency($speciality_id, $line_id, $month, $year)
    {
        return SpecialityFrequency::where('speciality_id', $speciality_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    /**
     * Optimized method to get speciality class frequency using computed columns
     */
    private function getSpecialityClassFrequency($speciality_id, $class_id, $line_id, $month, $year)
    {
        return SpecialityClassFrequency::where('speciality_id', $speciality_id)
            ->where('class_id', $class_id)
            ->where('line_id', $line_id)
            ->where('month', $month)
            ->where('year', $year)
            ->value('frequency');
    }

    



    // public function classType($account_id, $doctor_id, $line_id, $date)
    // {
    //     $fromMonth = Carbon::parse($date)->format('m');
    //     $fromYear = Carbon::parse($date)->format('Y');
    //     $doctor = Doctor::where('id', $doctor_id)->first();
    //     $other = OtherSetting::value('value');
    //     if ($other == 1) {
    //         return ClassFrequency::where('class_id', $doctor?->class_id)->where('line_id', $line_id)
    //             ->whereMonth('date', $fromMonth)
    //             ->whereYear('date', $fromYear)->value('frequency');
    //     } elseif ($other == 2) {
    //         return DoctorFrequency::where('doctor_id', $doctor?->id)->where('account_id', $account_id)
    //             ->where('line_id', $line_id)
    //             ->where('month', $fromMonth)
    //             ->where('year', $fromYear)
    //             ->whereNull('deleted_at')
    //             ->value('frequency');
    //     } elseif ($other == 3) {
    //         return SpecialityFrequency::where('speciality_id', $doctor->speciality_id)
    //             ->where('line_id', $line_id)->whereMonth('date', $fromMonth)
    //             ->whereYear('date', $fromYear)->value('frequency');
    //     } else {
    //         return SpecialityClassFrequency::where('speciality_id', $doctor->speciality_id)
    //             ->where('class_id', $doctor?->class_id)
    //             ->where('line_id', $line_id)->whereMonth('date', $fromMonth)
    //             ->whereYear('date', $fromYear)->value('frequency');
    //     }
    // }
}
