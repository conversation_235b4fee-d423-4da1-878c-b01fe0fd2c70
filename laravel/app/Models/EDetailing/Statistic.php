<?php

namespace App\Models\EDetailing;

use App\Exceptions\CrmException;
use App\Models\Attachment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Statistic extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'edetailing_statistics';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'visit_id',
        'user_id',
        'product_id',
        'presentation_id',
        'slide_id',
        'start',
        'end',
        'rate',
        'comment'
    ];

    public function presentation(): BelongsTo
    {
        return $this->belongsTo(Presentation::class);
    }

    public function slide(): BelongsTo
    {
        return $this->belongsTo(PresentationSlide::class);
    }

    protected $casts = [
        'start' => 'datetime',
        'end' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $appends = ['diffInSeconds'];

    public function getDiffInSecondsAttribute()
    {
        if (!empty($this->start) && !empty($this->end)) {
            return $this->end->diffInSeconds($this->start);
        }
    }
}
