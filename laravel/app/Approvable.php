<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Approvable extends Model
{
    protected $table = 'approvables';

    protected $fillable = ['approvable_id', 'approvable_type', 'line_id', 'file_id'];
    protected $hidden = ['pivot'];
    public function planables()
    {
        return $this->belongsToMany(Planable::class)->withPivot(['id', 'required', 'request_type', 'flow']);
    }

    public function approvable()
    {
        return $this->morphTo();
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }
    public function divisionType()
    {
        return $this->belongsTo(DivisionType::class, 'approvable_id')
            ->whereHas('approvables', function ($q) {
                $q->where('approvable_type', DivisionType::class);
            });
    }
    public function position()
    {
        return $this->belongsTo(Position::class, 'approvable_id')->where('approvable_type', Position::class);
    }
}
