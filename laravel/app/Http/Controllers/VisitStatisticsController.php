<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\ActualVisit;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\Models\ChangePlan;
use App\Models\DoubleVisitType;
use App\Models\Kpi;
use App\Models\OffDay;
use App\OwActualVisit;
use App\OwPlanVisit;
use App\PlanSetting;
use App\PlanVisit;
use App\Position;
use App\PublicHoliday;
use App\Services\Enums\KPITypes;
use App\Services\PostVisitKpisService;
use App\Shift;
use App\User;
use App\UserPosition;
use App\Vacation;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use function PHPUnit\Framework\isNull;

class VisitStatisticsController extends ApiController
{

    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    public function getPositions(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $roles = collect([]);
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            foreach ($lines as $line) {
                $roles = $roles->merge(collect([...$line->divisionTypes, ...$line->getLinePositions()])
                    ->map(fn($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name]));
            }
        } else {
            $divisionTypes = collect([]);
            $divisions = collect([]);
            foreach ($lines as $line) {
                if ($user->hasDivision($line, $from, $to)) {
                    $user->allBelowDivisions($line, $from, $to)->each(function ($division) use ($divisionTypes) {
                        $divisionTypes = $divisionTypes->push($division->DivisionType);
                    });
                    $divisionTypes = $divisionTypes->prepend($user->divisionType($line, $from, $to));
                }
                if ($user->hasPosition()) {
                    $userPosition = $user->userPosition->first();
                    $divisions = $userPosition->divisions->where('line_id', $line->id);
                    if (count($divisions) == 0) {
                        $divisions = LineDivision::where('line_id', $line->id)
                            ->where('division_type_id', 1)->get();
                    }
                    $divisions->values()->each(function ($division) use (&$divisions) {
                        $divisions = $divisions->merge($division?->getBelowDivisions());
                    });
                    $divisions->unique('id')->values()->each(function ($division) use ($divisionTypes) {
                        $divisionTypes = $divisionTypes->push($division->divisionType);
                    });
                    $divisionTypes = $divisionTypes->prepend($user->position());
                }
            }
            // throw new CrmException($divisionTypes);
            $roles = collect([...$divisionTypes->unique('name')->values()])
                ->map(fn($role) => ['id' => $role->id . '_' . get_class($role), 'name' => $role->name]);
        }
        return $this->respond($roles->unique('id')->values());
    }

    public function getPositionUsers(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $users = collect([]);
        if ($request->role_type == Position::class) {
            $users = UserPosition::whereHas('lines', fn($q) => $q->whereIn('line_id', $request->lines))->where('position_id', $request->role_id)->with('user')->get()->pluck('user');
        } else {
            foreach ($lines as $line) {
                $divisions = $user->userDivisions($line, $from, $to)->prepend($user->divisions($from, $to)->where('line_divisions.line_id', $line->id)->where('is_kol', 0)->first())
                    ->where('division_type_id', $request->role_id)->where('is_kol', 0);
                $divisions->each(function ($division) use ($users, $from, $to) {
                    $users = $users->push($division->users($from, $to)->first());
                });
            }
        }
        $users = $users->filter(fn($user) => $user != null)->unique('id')->values();
        $codes = $users->filter(fn($user) => $user->emp_code != null)->values();
        return response()->json(['users' => $users,'codes' => $codes]);
    }

    public function getAccountTypes(Request $request)
    {
        $accountTypes = AccountType::whereIntegerInRaw('shift_id', $request->shifts)->whereNull('deleted_at')->orderBy('sort', 'ASC')->get();
        return $this->respond($accountTypes);
    }

    public function actuals($object, $table, $visit, $from, $to)
    {
        $data = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.line_id',
            'actual_visits.div_id',
            'actual_visits.user_id',
            'actual_visits.plan_id',
            'actual_visits.visit_type_id',
            'actual_visits.acc_type_id',
            'actual_visits.double_visit_type_id',
            'actual_visits.account_id',
            DB::raw('DATE_FORMAT(crm_actual_visits.visit_date,"%Y-%m-%d") as start'),
            'bricks.name as brick',
            'employees.fullname as employee',
            'lines.name as line',
            'line_divisions.name as division',
            'division_types.color as color',
            'accounts.name as account',
            'accounts.id as account_id',
            'account_types.name as acc_type',
            'account_types.shift_id as acc_shift_id',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            'visit_types.name as type',
            'specialities.id as speciality_id',
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('users as employees', 'actual_visits.user_id', 'employees.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('shifts', 'account_types.shift_id', 'shifts.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->where($table, $object->id)
            ->whereBetween('visit_date', [$from, $to]);

        if (!empty($visit['types'])) {
            $data = $data->whereIntegerInRaw('actual_visits.acc_type_id', $visit['types']);
        }
        if (!empty($visit['lines'])) {
            $data = $data->whereIntegerInRaw('actual_visits.line_id', $visit['lines']);
        }
        if (!empty($visit['shifts'])) {
            $data = $data->whereIntegerInRaw('shifts.id', $visit['shifts']);
        }
        $data = $data->groupBy("actual_visits.id")->get();
        if ($visit['countBy'] == 1) {
            $data = $data->unique(function ($item) {
                // throw new CrmException($item->start);
                return $item->start . $item->account_id;
            })->values();
        }
        return $data;
    }

    public function plans($object, $table, $visit, $from, $to)
    {
        $data = DB::table('planned_visits')->select(
            'planned_visits.id',
            'planned_visits.account_id as account_id',
            DB::raw('DATE_FORMAT(crm_planned_visits.visit_date,"%Y-%m-%d") as date'),
            'users.fullname as user',
            'lines.name as line',
            'visit_types.name as type',
            'line_divisions.name as division',
            // DB::raw('IFNULL(crm_accounts.id,"") as account_id'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            DB::raw('IFNULL(crm_account_types.name,"") as acc_type'),
            DB::raw('IFNULL(crm_shifts.name,"") as shift'),
            'plan_visit_details.approval as status',
        )
            ->leftJoin('lines', 'planned_visits.line_id', 'lines.id')
            ->leftJoin('users', 'planned_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'planned_visits.div_id', 'line_divisions.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('planned_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', PlanVisit::class);
                }
            )
            ->leftJoin('shifts', 'planned_visits.shift_id', 'shifts.id')
            ->leftJoin('accounts', 'planned_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('doctors', 'planned_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'planned_visits.visit_type', 'visit_types.id')
            ->whereNull('planned_visits.deleted_at')
            ->where($table, $object->id)
            // ->where('planned_visits.line_id', $visit['line'])
            ->whereBetween('visit_date', [$from, $to]);
        // ->where('plan_visit_details.approval', 1);
        if (!empty($visit['types'])) {
            $data = $data->whereIntegerInRaw('account_types.id', $visit['types']);
        }
        if (!empty($visit['lines'])) {
            $data = $data->whereIntegerInRaw('planned_visits.line_id', $visit['lines']);
        }
        if (!empty($visit['shifts'])) {
            $data = $data->whereIntegerInRaw('account_types.shift_id', $visit['shifts']);
        }
        $data = $data->get();
        if ($visit['countBy'] == 1) {
            $data = $data->unique(function ($item) {
                return $item->date . $item->account_id;
            })->values();
        }
        // throw new CrmException($data);
        return $data;
    }

    public function prepareTableHeaders($accountTypeNames, $shiftNames, $double_visit_types): array
    {
        $fields = ['line', 'division', 'employee', 'emp_code', 'planned_days', 'plans', 'actual_from_plan', 'achievement', 'unplanned', 'single', 'single_days', 'double', 'double_days'];
        $clickable_fields = ['planned_days', 'plans', 'actual_from_plan', 'unplanned', 'single', 'single_days', 'double'];


        if (count($double_visit_types) > 0) {
            foreach ($double_visit_types as $type) {
                $fields[] = $type->name;
                $clickable_fields[] = $type->name;
            }
        }
        $fields = [...$fields, ...$accountTypeNames, ...$shiftNames];
        $clickable_fields = [...$clickable_fields, ...$accountTypeNames, ...$shiftNames];

        $fields = [...$fields, 'total_actual', 'actual_days', 'avg_visits', 'vac', 'plan_ow', 'actual_ow', 'change_plans', 'pending_plans', 'disapproved_plans'];

        $clickable_fields = [...$clickable_fields, 'total_actual', 'actual_days', 'vac', 'plan_ow', 'actual_ow', 'change_plans', 'pending_plans', 'disapproved_plans'];

        return [$fields, $clickable_fields];
    }

    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $visit = $request->visitFilter;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $shifts = Shift::select('id', 'name')->whereIntegerInRaw('id', $visit['shifts'])->get();
        $shiftNames = $shifts->pluck('name');
        $planSetting = PlanSetting::where('key', 'plan_shift')->value('value');
        $accountTypes = AccountType::select('id', 'name')->whereIntegerInRaw('id', $visit['types'])->get();
        $accountTypeNames = $accountTypes->pluck('name');
        $double_visit_types = DoubleVisitType::select('id', 'name')->get();

        [$fields, $clickable_fields] = $this->prepareTableHeaders($accountTypeNames, $shiftNames, $double_visit_types);


        $lines = Line::select(['id', 'name'])->when(!empty($visit['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['lines']))->get();
        $lineIds = $lines->pluck('id');
        $filtered = new Collection([]);
        $data = new Collection([]);
        foreach ($lines as $line) {
            $filtered = match ($visit['filter']) {
                1 => $filtered->merge($this->filterDivisionsForLine($line, $visit, $authUser, $from, $to)),
                2 => $filtered->merge($this->filterUsersForLine($line, $visit, $from, $to, $authUser)),
                default => collect([]),
            };
        }
        $filtered = $filtered->unique('id')->values();
        $changePlans = ChangePlan::whereIntegerInRaw('user_id', $filtered->pluck('id'))->where(function ($query) use ($from, $to) {
            $query->whereBetween('from', [$from, $to])
                ->orWhereBetween('to', [$from, $to])
                ->orWhere(function ($query) use ($from, $to) {
                    $query->where('from', '<=', $from)
                        ->where('to', '>=', $to);
                });
        })->get();
        // throw new CrmException($filtered);
        $filtered->each(function ($object) use ($changePlans, $double_visit_types, $lineIds, $shifts, $accountTypes, $from, $to, $data, $visit, $planSetting, $month, $year) {
            $data = $data->push($this->stats($lineIds, $object, $shifts, $accountTypes, $visit, $visit['filter'], $from, $to, $planSetting, $month, $year, $double_visit_types, $changePlans));
        });
        return response()->json([
            'data' => $data,
            'dates' => $dates,
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }

    // Private method to handle division filtering
    private function filterDivisionsForLine($line, $visit, $authUser, $from, $to)
    {
        $divisions = $line->divisions($from, $to)
            ->when(!empty($visit['divisions']), fn($q) => $q->whereIn("line_divisions.id", $visit['divisions']))
            ->get();

        return $authUser->filterDivisions($line, $divisions, $visit, $from, $to);
    }

    // Private method to handle user filtering
    private function filterUsersForLine($line, $visit, $from, $to, $authUser)
    {
        if ($visit['position'] != null) {
            return User::whereIntegerInRaw("id", $visit['users'])->get();
        }

        if ($visit['selectAllUsers']) {
            $filteredUsers = collect([]);
            $users = collect([]);
            $userPositions = collect([]);
            if ($authUser->hasRole('admin') || $authUser->hasRole('sub admin') || $authUser->hasRole('Gemstone Admin')) {
                $users = DivisionType::select('id', 'parent_id', 'name', 'last_level')
                    ->where('level', 1)
                    ->first()?->divisions($from, $to)?->where('line_id', $line->id)
                    ->first()?->users($from, $to)
                    ->get();
                $userPositions = Position::with("users")->get()->pluck("users")->flatten();
            } else {
                $users = $line->users($from, $to)
                    ->when(!empty($visit['users']), fn($q) => $q->whereIn("line_users.user_id", $visit['users']))
                    ->get();
            }

            // Log::info($users);
            if (count($users) > 0)
                $filteredUsers = $authUser->filterUsers($line, $users, $visit, $from, $to);
            // Log::info($filteredUsers);
            // Fetching all user positions
            return $filteredUsers->merge($userPositions);
        } else {
            $users = $line->users($from, $to)
                ->when(!empty($visit['users']), fn($q) => $q->whereIn("line_users.user_id", $visit['users']))
                ->get();

            return $authUser->filterUsers($line, $users, $visit, $from, $to);
        }
    }

    private function stats($lineIds, $object, $shifts, $accountTypes, $visit, $filter, $from, $to, $planSetting, $month, $year, $double_visit_types, $changePlans)
    {
        $line_id = $visit['filter'] == 1 ? $object->line->id : $object->lines($from, $to)?->first()?->id;
        $plans = $this->plans($object, $filter == 1 ? 'line_divisions.id' : 'users.id', $visit, $from, $to);
        $approved = $plans->where('status', 1);
        $approvedPlansCount = $approved->count();
        $planned_days = $approved->pluck('date')->unique()->count();
        $disapproved = $plans->where('status', '<>', 1)->whereNotNull('status')->count();
        $pending = $plans->whereNull('status')->count();
        $changePlansCount = $changePlans->where('user_id', $object->id)->count();
        $actuals = $this->actuals($object, $filter == 1 ? 'line_divisions.id' : 'employees.id', $visit, $from, $to);
        $unplanned = $actuals->whereNull('plan_id')->count();
        $planned = $actuals->whereNotNull('plan_id')->count();
        $singleVisits = $actuals->where('visit_type_id', 1);
        $single = $singleVisits->count();
        $singleDays = $singleVisits->pluck('start')->unique()->count();
        $doubleVisits = $actuals->where('visit_type_id', 2);
        $double = $doubleVisits->count();
        $doubleDays = $doubleVisits->pluck('start')->unique()->count();
        $objectActuals = $unplanned + $planned;
        $actualDays = $actuals->unique('start')->count();
        $data = collect([
            'id' => $object->id,
            'line' => $visit['filter'] == 1 ? $object?->line?->name : $object->lines($from, $to)->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name')->implode(','),
            'division' => $visit['filter'] == 1 ? $object?->name : $object->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name')->implode(','),
            'employee' => $visit['filter'] == 1 ? $object->users($from, $to)?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)->pluck('fullname')->implode(',') : $object?->fullname,
            'emp_code' => $visit['filter'] == 1 ? ($object->users($from, $to)?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)->pluck('emp_code')->implode(',') ?? '') : $object?->emp_code ?? '',
            'color' => $visit['filter'] == 1 ? $object?->DivisionType->color : $object->divisions($from, $to)->where('is_kol', 0)->first()?->DivisionType->color,
            'planned_days' => $planned_days,
            'plans' => $approvedPlansCount,
            'actual_from_plan' => $planned,
            'achievement' => $approvedPlansCount ? round(($planned / $approvedPlansCount) * 100, 2) . '%' : 0 . '%',
            'unplanned' => $unplanned,
            'single' => $single,
            'single_days' => $singleDays,
            'double' => $double,
            'double_days' => $doubleDays,
        ]);
        if (count($double_visit_types) > 0) {
            $double_visit_types->each(function ($type) use ($data, $actuals) {
                $data->put($type->name, $actuals->where('double_visit_type_id', $type->id)->count());
            });
        }
        $accountTypes->each(function ($accountType) use ($data, $actuals) {
            $data->put($accountType->name, $actuals->where('acc_type_id', $accountType->id)->count());
        });
        $shifts->each(function ($shift) use ($data, $actuals) {
            $data->put($shift->name, $actuals->where('acc_shift_id', $shift->id)->count());
        });
        if ($filter == 2) {
            $data->put('vac', $this->vacations($object, $visit, $from, $to, $month, $year, $line_id));
            $data->put('plan_ow', $this->planOw($object, $from, $to, $visit, $line_id));
            $data->put('actual_ow', $this->ow($object, $from, $to, $visit, $line_id));
        }
        $data->put('actual_days', $actualDays);
        $data->put('total_actual', $objectActuals);
        $data->put('avg_visits', $actualDays > 0 ? round($objectActuals / $actualDays, 0) : 0);
        $data->put('change_plans', $changePlansCount);
        $data->put('pending_plans', $pending);
        $data->put('disapproved_plans', $disapproved);
        return $data;
    }

    public function vacations($user, $visit, $from, $to, $month, $year, $lineId)
    {
        return Vacation::getVacationsDatePerPeriod(
            user: $user,
            from: $from,
            to: $to,
            month: $month,
            year: $year,
            shifts: $visit['shifts'],
            line_id: $lineId
        );
    }

    public function planOw($user, $from, $to, $visit, $lineId)
    {
        $owVisits = OwPlanVisit::select('ow_plan_visits.day as date', 'user_id', 'shift_id')->where('user_id', $user->id)->whereBetween('day', [$from, $to])
            ->where(fn($q) => $q->whereIn('shift_id', $visit['shifts']))
            ->get();
        return $this->calculateOvertimeCount($owVisits, $visit, $lineId);
    }

    public function ow($user, $from, $to, $visit, $lineId)
    {
        $owVisits = OwActualVisit::select(['ow_actual_visits.date', 'ow_actual_visits.user_id', 'ow_actual_visits.shift_id'])
            ->leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', 'office_work_types.id')
            ->where('office_work_types.with_deduct', '1')
            ->where('ow_actual_visits.user_id', $user->id)->whereBetween('ow_actual_visits.date', [$from, $to])
            ->where(fn($q) => $q->whereIn('ow_actual_visits.shift_id', $visit['shifts'])->orWhere('ow_actual_visits.shift_id', null))
            ->get();
        return $this->calculateOvertimeCount($owVisits, $visit, $lineId);
    }

    private function calculateOvertimeCount($owVisits, $visit, $lineId)
    {
        $count = 0.0;
        $shifts = count($visit['shifts']) > 1 ? [1, 2] : $visit['shifts'];
        foreach ($owVisits as $owVisit) {
            if ($owVisit->shift_id != null) {
                $count += $this->checkOffDays($owVisit, $shifts, $owVisit->shift_id, $lineId);
            } else {
                foreach ($shifts as $shift) {
                    $count += $this->checkOffDays($owVisit, $shifts, $shift, $lineId);
                }
            }
        }
        return $count;
    }

    public function checkOffDays($owVisit, $shifts, $shift, $lineId)
    {
        $count = 0;
        if (
            !OffDay::isOffDay($owVisit->date, $owVisit->date, $owVisit->date, $shift, $lineId)
            && !PublicHoliday::isPublicHoliday($owVisit->date, $owVisit->date, $owVisit->date, $lineId)
        ) {
            count($shifts) > 1 ? $count = 0.5 : $count = 1.0;
        }
        return $count;
    }

    public function showData(Request $request)
    {
        $visit = $request->listFilter;
        $column = $request->column;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $account_type = AccountType::where("name", $column)->first();
        $double_visit_type = DoubleVisitType::where("name", $column)->first();
        $shift = Shift::where("name", $column)->first();
        $lineId = null;
        if ($request->div != null) {
            $object = LineDivision::find($request->div);
            $lineId = $object->line?->id;
        } else {
            $object = User::find($request->user);
            $lineId = $object->lines($from, $to)->first()?->id;
        }
        $data = null;
        $planSetting = PlanSetting::where('key', 'plan_shift')->value('value');
        $plans = $this->plans($object, $visit['filter'] == 1 ? 'planned_visits.div_id' : 'users.id', $visit, $from, $to, $planSetting);
        $actuals = $this->actuals($object, $visit['filter'] == 1 ? 'actual_visits.div_id' : 'employees.id', $visit, $from, $to);
        if ($column == 'plans') {
            $data = $plans->where('status', 1)->values();
        }
        if ($column == 'planned_days') {
            $data = $plans->where('status', 1)->unique('date')->values()->map(function ($plan) {
                return [
                    'day' => $plan->date
                ];
            });
        }
        if ($column == 'pending_plans') {
            $data = $plans->whereNull('status')->values();
        }
        if ($column == 'disapproved_plans') {
            $data = $plans->where('status', '<>', 1)->whereNotNull('status')->values();
        }
        if ($column == 'actual_from_plan') {
            $data = $actuals->whereNotNull('plan_id');
        }
        if ($column == 'unplanned') {
            $data = $actuals->whereNull('plan_id');
        }
        if ($column == 'single') {
            $data = $actuals->where('visit_type_id', 1);
        }
        if ($column == 'double') {
            $data = $actuals->where('visit_type_id', 2);
        }
        if ($double_visit_type?->name == $column) {
            $data = $actuals->where('double_visit_type_id', $double_visit_type->id);
        }
        if ($column == 'total_actual') {
            $data = $actuals;
        }
        if ($column == 'actual_days') {
            $data = $actuals->unique('start');
        }

        if ($account_type?->name == $column) {
            $data = $actuals->where('acc_type_id', $account_type->id);
        }
        if ($shift?->name == $column) {
            $data = $actuals->where('acc_shift_id', $shift->id);
        }
        if ($column != 'plans' && $column != 'planned_days' && $column != 'disapproved_plans' && $column != 'pending_plans' && $column != 'change_plans' && $column != 'actual_ow' && $column != 'plan_ow' && $column != 'vac') {
            $data = $data->values()->map(function ($actual) {
                return [
                    'id' => $actual->id,
                    'date' => $actual->start,
                    'plan_id' => $actual->plan_id ?? '',
                    'user' => $actual->employee,
                    'type' => $actual->type,
                    'manager' => $actual->actualVisitManagers->first()?->user?->fullname,
                    'mr' => $actual->isDouble() && !User::find($actual->user_id)
                        ->divisionType(Line::find($actual->line_id))?->isLastLevel() ? ActualVisit::whereDate('visit_date', $actual->start)
                        ->where('account_id', $actual->account_id)->where('line_id', $actual->line_id)
                        ->where('account_dr_id', $actual->doctor_id)
                        ->where('user_id', '<>', $actual->user_id)->first()?->user?->fullname : $actual->employee,
                    'line' => $actual->line,
                    'division' => $actual->division,
                    'account_id' => $actual->account_id,
                    'account' => $actual->account,
                    'doctor' => $actual->doctor != null ? $actual->doctor : "",
                    'doctor_id' => $actual->doctor_id != null ? $actual->doctor_id : "",
                    'speciality' => $actual->doctor_id != null ? $actual->speciality : "",
                    'acc_type' => $actual->account != null ? $actual->acc_type : "",
                    'color' => $actual->color,
                ];
            });
        }
        if ($visit['filter'] == 2) {
            if ($column == 'plan_ow') {
                $data = OwPlanVisit::select(['id', 'notes', 'ow_type_id', 'day', 'user_id', 'shift_id'])
                    ->where('user_id', $object->id)->whereBetween('day', [$from, $to])->whereIn('shift_id', $visit['shifts']);
                // }
                $data = $data->get()
                    ->filter(function ($ow) use ($from, $to, $lineId) {
                        return !OffDay::isOffDay($from, $to, $ow->date, $lineId);
                    })->map(function ($ow) {
                        return [
                            'id' => $ow->id,
                            'user' => $ow->user->fullname,
                            'day' => Carbon::parse($ow->day)->toDateString(),
                            'type' => $ow->owType->name,
                            'shift' => $ow->shift ? $ow->shift->name : 'Full Day',
                            'notes' => $ow->notes ?? '',
                        ];
                    });
            }
            if ($column == 'actual_ow') {
                $shifts = count($visit['shifts']) > 1 ? [1, 2] : $visit['shifts'];
                $data = OwActualVisit::select([
                    'ow_actual_visits.id',
                    'ow_actual_visits.notes',
                    'ow_actual_visits.ow_type_id',
                    'ow_actual_visits.date',
                    'ow_actual_visits.user_id',
                    'ow_actual_visits.shift_id'
                ])
                    ->leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', 'office_work_types.id')
                    ->where('office_work_types.with_deduct', '1')
                    ->where('ow_actual_visits.user_id', $object->id)
                    ->whereBetween('ow_actual_visits.date', [$from, $to]);
                $data = $data->where(fn($q) => $q->whereIn('ow_actual_visits.shift_id', $visit['shifts'])->orWhere('ow_actual_visits.shift_id', null))
                    ->get()->filter(function ($owVisit) use ($shifts, $lineId) {
                        if ($owVisit->shift_id != null) {
                            if (
                                !OffDay::isOffDay($owVisit->date, $owVisit->date, $owVisit->date, $owVisit->shift_id, $lineId)
                                && !PublicHoliday::isPublicHoliday($owVisit->date, $owVisit->date, $owVisit->date, $lineId)
                            ) {
                                return [$owVisit];
                            }
                        } else {
                            foreach ($shifts as $shift) {
                                if (
                                    !OffDay::isOffDay($owVisit->date, $owVisit->date, $owVisit->date, $shift, $lineId)
                                    && !PublicHoliday::isPublicHoliday($owVisit->date, $owVisit->date, $owVisit->date, $lineId)
                                ) {
                                    return [$owVisit];
                                }
                            }
                        }
                    })->values()->map(function ($ow) {
                        return [
                            'id' => $ow->id,
                            'user' => $ow->user->fullname,
                            'date' => Carbon::parse($ow->date)->toDateString(),
                            'type' => $ow->owType->name,
                            'shift' => $ow->shift ? $ow->shift->name : 'Full Day',
                            'notes' => $ow->notes ?? '',
                        ];
                    });
            }
            if ($column == 'vac') {
                $shifts = count($visit['shifts']) > 1 ? [1, 2] : $visit['shifts'];
                $data = Vacation::select('id', 'user_id', 'vacation_type_id', 'from_date', 'to_date', 'full_day', 'shift_id')
                    ->where(fn($q) => $q->whereIn('shift_id', $visit['shifts'])->orWhereNull('shift_id'))
                    ->where('user_id', $object->id)
                    ->where(fn($q) => $q->whereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"), $month)
                        ->orWhereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%Y-%m'))"), [Carbon::parse($from)->addYear(-1)->format('Y-m'), Carbon::parse($to)->format('Y-m')]))
                    ->where(fn($q) => $q->whereYear('vacations.from_date', $year)->orWhereYear('vacations.to_date', $year));
                $data = $data->whereHas('details', function ($q) {
                    $q->where('approval', 1);
                })->get()->filter(function ($vacation) use ($from, $to, $shifts, $lineId) {
                    $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
                    foreach ($period as $date) {
                        foreach ($shifts as $shift) {
                            if ($date->between($from, $to)) {
                                if (
                                    !OffDay::isOffDay($vacation->from_date, $vacation->to_date, $date, $shift, $lineId) &&
                                    !PublicHoliday::isPublicHoliday($from, $to, $date, $lineId)
                                ) {
                                    return [$vacation];
                                }
                            }
                        }
                    }
                });
                $data = $data->values()->map(function ($vacation) {
                    return [
                        'id' => $vacation->id,
                        'user' => $vacation->user->fullname,
                        'from_date' => Carbon::parse($vacation->from_date)->toDateString(),
                        'to_date' => Carbon::parse($vacation->to_date)->toDateString(),
                        'type' => $vacation->type->name,
                        'shift' => $vacation->shift ? $vacation->shift->name : 'Full Day',
                        'notes' => $vacation->notes ?? '',
                    ];
                });
            }
            if ($column == 'change_plans') {
                $data = ChangePlan::where('user_id', $object->id)->where(function ($query) use ($from, $to) {
                    $query->whereBetween('from', [$from, $to])
                        ->orWhereBetween('to', [$from, $to])
                        ->orWhere(function ($query) use ($from, $to) {
                            $query->where('from', '<=', $from)
                                ->where('to', '>=', $to);
                        });
                })->get()->map(function ($ow) {
                    return [
                        'id' => $ow->id,
                        'employee' => $ow->user->fullname,
                        'from' => Carbon::parse($ow->from)->toDateString(),
                        'to' => Carbon::parse($ow->to)->toDateString(),
                    ];
                });
            }
        }


        return $this->respond($data);
    }
    public function post(Request $request)
    {
        $statistics = $request->data;
        $filters = $request->filters;
        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $period = CarbonPeriod::create($from, '1 month', $to);

        $changPlanKpi = Kpi::where('name', 'Change Plan')->first();
        $planAchievementKpi = Kpi::where('name', 'Plan Achievement')->first();
        $this->postVisitKpisService->post(KPITypes::CHANGE_PLAN, $statistics, $period, $changPlanKpi);
        $this->postVisitKpisService->post(KPITypes::PLAN_ACHIEVEMENT, $statistics, $period, $planAchievementKpi);
        return $this->respondSuccess();
    }
}
