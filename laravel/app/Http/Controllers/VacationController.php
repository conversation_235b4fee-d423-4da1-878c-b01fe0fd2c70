<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\VacationRequest;
use App\Shift;
use App\Vacation;
use App\VacationAttachment;
use App\VacationType;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Http\Requests\MailRequest;
use App\LineUser;
use App\Models\ApprovalFlowUser;
use App\Models\Attachment;
use App\Models\OffDay;
use App\Models\Policy;
use App\Models\RequestFeedback;
use App\Models\StartExpenseMonth;
use App\Notifications\VacationCreatedNotification;
use App\PlanVisitDetails;
use App\PublicHoliday;
use App\VacationSetting;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;

class VacationController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $vacations = Vacation::select(
            'vacations.id',
            'vacations.from_date as from',
            'vacations.to_date as to',
            DB::raw('IFNULL(crm_shifts.name,"Full Day") as shift'),
            'vacation_types.name as type',
            'users.fullname as user',
            'vacations.notes',
            'plan_visit_details.approval as status',
            // DB::raw('group_concat(distinct crm_plan_visit_details.approval) as status')
        )
            ->leftJoin('users', 'vacations.user_id', '=', 'users.id')
            ->leftJoin('vacation_types', 'vacations.vacation_type_id', '=', 'vacation_types.id')
            ->leftJoin('shifts', 'vacations.shift_id', '=', 'shifts.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('vacations.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', Vacation::class);
                }
            );
        if (
            request('query') == "Full Day" ||
            request('query') == "Full" ||
            request('query') == "Day" ||
            request('query') == "Full Day "
        ) {
            $vacations = $vacations->whereNull('vacations.shift_id');
        } else {
            $vacations = $vacations->where(fn($q) => $q->where('vacations.id', 'Like', '%' . request('query') . '%')
                ->orWhere('vacations.from_date', 'Like', '%' . request('query') . '%')
                ->orWhere('vacations.to_date', 'Like', '%' . request('query') . '%')
                ->orWhere('users.fullname', 'Like', '%' . request('query') . '%')
                ->orWhere('vacation_types.name', 'Like', '%' . request('query') . '%')
                ->orWhere('shifts.name', 'Like', '%' . request('query') . '%'));
        }

        $users = $user->indexPerUser($user);
        if (count($users) > 0) {
            $vacations = $vacations->whereIntegerInRaw('vacations.user_id', $users->values());
        }
        $vacations = $vacations->orderBy('from_date', 'desc')->groupBy('id', 'plan_visit_details.approval')->get();
        // $setting = App\VacationSetting::where('key', 'delete_edit_vacation')->value('value');
        LogActivity::addLog();
        return $this->respondAll($vacations);
    }
    public function setting()
    {
        $setting = VacationSetting::where('key', 'delete_edit_vacation')->value('value');
        return $this->respond($setting);
    }

    public function vacationFlow(Request $request)
    {
        $vacation = Vacation::find($request->id);
        $approvalFlow = $vacation->details?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($vacation->details?->approvalFlows()->get());
        } else {
            $data = $data->push($vacation->details);
        }
        $data = $data->map(function ($detail) use ($vacation) {
            // throw new CrmException(($detail));
            return [
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'status' => $detail?->approval ?? null,
                'description' => $detail?->description ?? '',
                'reason' => $detail?->approval == 0 && $vacation->reasons()->exists() ? $vacation->reasons->first()?->reason : "",
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateTimeString(),
            ];
        });
        return $this->respond($data);
    }

    private function getStatus($vacation)
    {
        if ($vacation->details?->approval === null) return null;
        if ($vacation->details?->approval === 1) return 1;
        if ($vacation->details?->approval === 0) return 0;
    }

    public function minDate()
    {
        $vactionSetting = VacationSetting::where('key', 'vacation_time')->value('value');
        $vacationTime = StartExpenseMonth::where('name', $vactionSetting)->value('day');
        $min = Carbon::now()->addMonth($vacationTime)->firstOfMonth();
        $max = Carbon::now()->addMonth(-1)->endOfMonth();
        $min_date = Carbon::parse($min)->toDateString();
        $max_date = Carbon::parse($max)->toDateString();
        return response()->json(['min_date' => $min_date, 'max_date' => $max_date, 'vacationTime' => $vacationTime]);
    }

    public function vacationDates($id)
    {
        $vacationType = VacationType::find($id);
        $fromDate = null;
        if ($vacationType->startDate) {
            $fromDate = Carbon::now()->addDays($vacationType->startDate->day)->toDateString();
        } else {
            $fromDate = Carbon::now()->firstOfMonth()->addMonth(-1)->toDateString();
        }
        return response()->json(['fromDate' => $fromDate]);
    }

    public function resetApprovals(Request $request)
    {
        $vacation = Vacation::find($request->id);
        $approvalFlow = $vacation->details?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($vacation->details?->approvalFlows()->get());
        } else {
            $data = $data->push($vacation->details);
        }
        $detail_id = $data[0]->detail_id;
        if (!isNullable($detail_id))
            $vacation->details?->approvalFlows()->delete();

        $vacation->details->approval = null;
        $vacation->details->user_id = null;
        $vacation->details->save();
        return $this->respondSuccess();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $from_date = Carbon::parse()->toDateString();
        $to_date = Carbon::parse()->endOfMonth()->format('Y-m-d');
        $setting = VacationSetting::where('key', 'vacation_time')->value('value') == 'Now';
        $vacationTypes = VacationType::orderBy('sort', 'ASC')->get();
        $shifts = Shift::select('shifts.id', 'shifts.name')->whereIntegerInRaw('id', [1, 2])->orderBy('sort', 'ASC')->get();
        return response()->json([
            'description' => 'success',
            'setting' => $setting,
            'from_date' => $from_date,
            'to_date' => $to_date,
            'shifts' => $shifts,
            'vacation_types' => $vacationTypes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(VacationRequest $request)
    {
        DB::transaction(function () use ($request) {
            /**@var User $user */
            $user = Auth::user();
            $line = $user->lines()->first();
            $hasApprovable = $user->division($line)?->divisionType?->planable?->where('line_id', $line->id)->first()?->approvables()?->wherePivot('request_type', Vacation::class)->count() > 0;
            if ($user->hasPosition()) {
                $hasApprovable = $user->position()->planable
                    ->first()->approvables()->wherePivot('request_type', Vacation::class)->count() > 0;
            }

            $vacation = Vacation::create([
                'user_id' => $user->id,
                'from_date' => $request->from_date,
                'to_date' => $request->to_date,
                'vacation_type_id' => $request->vacation_type_id,
                'shift_id' => isNullable($request->shift_id) ? null : $request->shift_id,
                'notes' => isNullable($request->notes) ? null : $request->notes,
                'full_day' => $request->full_day,
            ]);
            foreach ($request->attachments as $attachment) {
                Attachment::create([
                    'attachable_id' => $vacation->id,
                    'attachable_type' => Vacation::class,
                    'path' => $attachment,
                ]);
            }
            PlanVisitDetails::firstOrCreate([
                'visitable_id' => $vacation->id,
                'visitable_type' => Vacation::class,
                'approval' => $hasApprovable ? null : 1
            ]);
            NotificationHelper::send(
                collect($user->approvableUsers($user->lines()->first()?->id ?? 1)),
                new VacationCreatedNotification('Vacation Created', auth()->user())
            );

            $model_id = $vacation->id;
            $model_type = Vacation::class;
            LogActivity::addLog($model_id, $model_type);
        });

        return response()->json(['status' => 'success']);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $vacation = DB::table('vacations')
            ->select(
                'vacations.id',
                DB::raw('DATE_FORMAT(crm_vacations.from_date,"%Y-%m-%d") as from_date'),
                DB::raw('DATE_FORMAT(crm_vacations.created_at,"%Y-%m-%d") as created_at'),
                DB::raw('DATE_FORMAT(crm_users.hiring_date,"%Y-%m-%d") as hiring'),
                DB::raw('DATE_FORMAT(crm_vacations.to_date,"%Y-%m-%d") as to_date'),
                DB::raw('IFNULL(crm_shifts.name,"Full Day") as shift'),
                DB::raw('crm_shifts.id  as shift_id'),
                'vacation_types.name as vacation',
                'vacations.notes',
                'vacations.full_day as full_day',
                'users.fullname as user',
                'users.id as user_id',
                'users.emp_code as emp_code',
                'users.menuroles as position',
                DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            )
            ->leftJoin('vacation_types', 'vacations.vacation_type_id', '=', 'vacation_types.id')
            ->leftJoin('users', 'vacations.user_id', '=', 'users.id')
            ->leftJoin('line_users', function ($join) {
                $join->on('users.id', 'line_users.user_id')
                    ->where('line_users.from_date', '<=', (string)Carbon::now())
                    ->whereNull('line_users.deleted_at')
                    ->where(fn($q) => $q->where('line_users.to_date', '>=', (string)Carbon::now())
                        ->orWhere('line_users.to_date', null));
            })
            ->leftJoin('lines', 'line_users.line_id', 'lines.id')
            ->leftJoin('shifts', 'vacations.shift_id', '=', 'shifts.id')
            ->where('vacations.id', '=', $id)
            ->groupBy('vacations.id')
            ->first();
        $user_id = $vacation->user_id;
        $vacationsWithTypes = [];
        $year = Carbon::now()->format('Y');
        $from = Carbon::now()->firstOfYear();
        $to = Carbon::now()->lastOfYear();
        $totalDays = 0;
        VacationType::get()->each(function ($type) use ($vacation, &$totalDays, &$vacationsWithTypes, $user_id, $year, $from, $to) {
            $getDays = $this->getVacationsOfYearPerType($vacation, $user_id, $from, $to, $year, [1, 2], $type);
            $totalDays += $getDays;
            $vacationsWithTypes[] = [
                'type' => $type->name,
                'count' => $getDays
            ];
        });
        $shifts = $vacation->shift_id ? [$vacation->shift_id] : [1, 2];
        $count = Vacation::getVacationsDatePerPeriodWithoutStatus(Auth::user(), $vacation, $shifts);
        $feedbacks = RequestFeedback::where('requestable_id', $id)->where('requestable_type', Vacation::class)->get()->map(function ($feedback) {
            return [
                'id' => $feedback->id,
                'user' => $feedback->user->fullname,
                'emp_code' => $feedback->user->emp_code,
                'position' => $feedback->user->menuroles,
                'feedback' => $feedback->feedback,
                'auth' => $feedback->user->id == Auth::id() ? 1 : 0,
            ];
        });
        $attachments = Attachment::where('attachable_id', $id)->where('attachable_type', Vacation::class)->get()->map(function ($vacation) {
            return [
                'id' => $vacation->attachable_id,
                'file' => $vacation->path,
            ];
        });
        $model_id = $id;
        $model_type = Vacation::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json([
            'vacation' => $vacation,
            'vacationsWithTypes' => $vacationsWithTypes,
            'totalDays' => $totalDays,
            'feedbacks' => $feedbacks,
            'attachments' => $attachments,
            'count' => $count
        ]);
    }
    private function getVacationsOfYearPerType($vacation, $user_id, $from, $to, $year, $shifts, $type)
    {
        $vacations = Vacation::select('id', 'user_id', 'from_date', 'to_date', 'full_day', 'shift_id')
            ->whereNot('id', $vacation->id)
            ->where('user_id', $user_id)->where('vacation_type_id', $type->id)
            ->where(fn($q) => $q->whereIntegerInRaw('shift_id', $shifts)->orWhereNull('shift_id'))
            ->where(fn($q) => $q->whereYear('vacations.from_date', $year)->orWhereYear('vacations.to_date', $year))
            ->whereHas('details', function ($q) {
                $q->where('approval', 1);
            })->get();
        $count = 0.0;
        $shifts = count($shifts) > 1 ? [1, 2] : $shifts;
        foreach ($vacations as $vacation) {
            $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
            foreach ($period as $date) {
                foreach ($shifts as $shift) {
                    if ($date->between($from, $to)) {
                        if (
                            !OffDay::isOffDay($vacation->from_date, $vacation->to_date, $date, $shift) &&
                            !PublicHoliday::isPublicHoliday($from, $to, $date)
                        ) {
                            if ($vacation->shift_id == $shift) {
                                count($shifts) > 1 ? $count += 0.5 : $count += 1.0;
                            }
                            if ($vacation->full_day == 1) {
                                count($shifts) > 1 ? $count += 0.5 : $count += 1.0;
                            }
                        }
                    }
                }
            }
        }
        return $count;
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $vacation = Vacation::find($id);
        $vacation_types = VacationType::get();
        $shifts = Shift::get();
        $shifts = $shifts->push(['id' => 0, 'name' => 'Full Day']);
        $model_id = $id;
        $model_type = Vacation::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'vacation' => $vacation,
            'vacation_types' => $vacation_types,
            'shifts' => $shifts,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // throw new CrmException($request->all());
        $vacation = Vacation::find($id);
        $auth = Auth::user();
        if ($auth->id == $request->user_id)
            $vacation->user_id = $auth->id;
        else
            $vacation->user_id = $request->user_id;

        $vacation->from_date = $request->input('from_date');
        $vacation->full_day = 0;
        $vacation->to_date = $request->input('to_date');
        $vacation->vacation_type_id = $request->input('vacation_type_id');
        if ($request->shift_id == 0) {
            $vacation->shift_id = null;
            $vacation->full_day = 1;
        } else {
            $vacation->shift_id = $request->input('shift_id');
        }
        if (!empty($request->attachments)) {
            // throw new CrmException($request->attachments);
            $vacation->attachments()->forceDelete();
            foreach ($request->attachments as $attachment) {
                Attachment::create([
                    'attachable_id' => $vacation->id,
                    'attachable_type' => Vacation::class,
                    'path' => $attachment,
                ]);
            }
        }

        $vacation->notes = $request->input('notes');
        $vacation->save();
        $model_id = $id;
        $model_type = Vacation::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $vacation = Vacation::find($id);
        if ($vacation) {
            $vacation->attachments()->delete();
            $vacation->delete();
        }
        $model_id = $id;
        $model_type = Vacation::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function exportvacations()
    {
        return Vacation::export(ExcelType::XLSX);
    }
    public function exportvacationsCsv()
    {
        return Vacation::export(ExcelType::CSV);
    }

    public function exportvacationspdf()
    {
        $vacations = Vacation::where('deleted_at', null);
        /**@var User $user */
        $user = Auth::user();
        $allUsers = $user->allBelowUsers()->pluck('id')->push(auth()->id());
        if (!$user->hasRole('admin'))
            $vacations = $vacations->whereIn('user_id', $allUsers);
        $vacations = $vacations->get();
        return Vacation::exportPdf($vacations);
    }

    public function sendvacationsmail(MailRequest $request)
    {
        $vacations = Vacation::where('deleted_at', null)->get();
        return Vacation::sendMail($request, $vacations);
    }


    public function feedback($id, Request $request)
    {
        $vavation = Vacation::find($id);
        $user_id = Auth::id();
        $vavation_feedback = new RequestFeedback();
        $vavation_feedback->requestable_type = Vacation::class;
        $vavation_feedback->requestable_id = $vavation->id;
        $vavation_feedback->feedback = $request->feedback;
        $vavation_feedback->user_id = $user_id;
        $vavation_feedback->save();
        return $this->respondSuccess();
        // throw new CrmException($vavation_feedback);

    }

    public function policies()
    {
        $policies = Policy::where('policiable_type', Vacation::class)->get();
        return $this->respond(['policies' => $policies]);
    }
}
