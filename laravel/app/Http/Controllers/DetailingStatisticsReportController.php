<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\Line;
use App\LineDivision;
use App\Models\Attachment;
use App\Models\EDetailing\Presentation;
use App\Models\EDetailing\PresentationSlide;
use App\Models\EDetailing\Statistic;
use App\Product;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DetailingStatisticsReportController extends ApiController
{
    public function actualVisits($visit, $object, $table, $from, $to, $products)
    {
        $data = ActualVisit::select(
            'actual_visits.id as id',
            'actual_visits.line_id',
            'actual_visits.div_id',
            'actual_visits.user_id',
            'actual_visits.acc_type_id',
            'actual_visits.visit_date',
            DB::raw("DATE_FORMAT(crm_actual_visits.created_at,'%Y-%m-%d %H:%s:%i') as date"),
            'actual_visit_products.product_id',
            'products.name',
            'actual_visits.account_id',
            'bricks.name as brick',
            'products.name as product',
            'lines.name as line',
            'line_divisions.name as division',
            'accounts.name as account',
            'account_types.name as acc_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            'visit_types.name as type',
            'plan_visit_details.approval as status'
        )
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->where('actual_visits.line_id', $visit['line'])
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->whereIn('actual_visit_products.product_id', $products->pluck('id')->toArray())
            ->where($table, $object->id)
            ->whereBetween('actual_visits.visit_date', [$from, $to]);
        if (!empty($visit['types'])) {
            $data = $data->whereIn('actual_visits.acc_type_id', $visit['types']);
        }
        if (!empty($visit['specialities'])) {
            $data = $data->whereIn('specialities.id', $visit['specialities']);
        }
        $data = $data->get();
        return $data;
    }
    public function filter(Request $request, Line $line)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $visit = $request->visitFilter;
        // throw new CrmException($visit);
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();

        $products = $line->products($from, $to)->select('products.id', 'products.name')
            ->when(!empty($visit['products']), fn($q) => $q->whereIn("products.id", $visit['products']))->get();
        $fields = collect(['line', 'division', 'employee', 'emp_code']);
        $clickable_fields = collect([]);
        foreach ($products as $product) {
            $fields = $fields->push($product->name . ' ' . '(calls)');
            $fields = $fields->push($product->name . ' ' . '(detail_mins)');
            $clickable_fields = $clickable_fields->push($product->name . ' ' . '(calls)');
            $clickable_fields = $clickable_fields->push($product->name . ' ' . '(detail_mins)');
        }

        $filtered = new Collection([]);
        $data = new Collection([]);
        $filter = $visit['filter'];
        if ($filter == 1) {
            $divisions = $line->divisions()->where("deleted_at", null)
                ->when(!empty($visit['divisions']), fn($q) => $q->whereIn("line_divisions.id", $visit['divisions']))->get();
            $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $visit));
        }
        if ($filter == 2) {
            $users = $line->users()->wherePivot("deleted_at", null)
                ->when(!empty($visit['users']), fn($q) => $q->whereIn("line_users.user_id", $visit['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $visit));
        }
        if ($visit['view'] == 'Statistics') {
            $filtered->each(function ($object) use ($line, $products, $data, $visit, $from, $to, $filter) {
                $data = $data->push($this->statistics($line, $object, $products, $from, $to, $visit, $filter));
            });
        } else {
            $fields = ['id', 'visit_id', 'account', 'doctor', 'product', 'presentation', 'slide', 'start', 'end','rate', 'diffInSeconds'];
            $table = $filter == 2 ? 'users.id' : 'line_divisions.id';
            $data = $this->details($line, $filtered->pluck('id')->toArray(), $table, $products, $from, $to, $visit, $filter);
        }
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }
    private function statistics($line, $object, $products, $from, $to, $visit, $filter)
    {
        $data = collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $filter == 1 ? $object?->name : $object->division($line)?->name,
            'employee' => $filter == 1 ? $object->users()?->first()?->name : $object?->fullname,
            'emp_code' => $filter == 1 ? $object->users()?->first()?->emp_code : $object?->emp_code,
            'color' => $filter == 1 ? $object?->DivisionType->color : $object->division($line)?->DivisionType->color,
        ]);
        $actuals = $this->actualVisits($visit, $object, $filter == 1 ? 'line_divisions.id' : 'users.id', $from, $to, $products);
        $products->each(function ($product) use ($data, $actuals) {
            $countCalls = $actuals->where('product_id', $product->id)->count();
            $data->put($product->name . ' ' . '(calls)', $countCalls);
            $data->put($product->name . ' ' . '(detail_mins)', $this->detailing($product, $actuals));
        });
        return $data;
    }

    private function details($line, $objects, $table, $products, $from, $to, $visit, $filter)
    {
        return Statistic::select(
            'edetailing_statistics.id as id',
            'edetailing_statistics.visit_id as visit_id',
            'accounts.name as account',
            'doctors.name as doctor',
            'products.name as product',
            'edetailing_statistics.start as start',
            'edetailing_statistics.end as end',
            DB::raw('IFNULL(crm_edetailing_statistics.rate,0) as rate'),
            'presentations.name as presentation',
            'attachments.path as slide',
            // 'edetailing_statistics.diffInMinutes as minutes',
        )
            ->leftJoin('actual_visits', 'edetailing_statistics.visit_id', 'actual_visits.id')
            ->leftJoin('presentations', 'edetailing_statistics.presentation_id', 'presentations.id')
            ->leftJoin('presentation_slides', 'edetailing_statistics.slide_id', 'presentation_slides.id')
            ->leftJoin('attachments', function ($join) {
                $join->on('presentation_slides.resource_id', 'attachments.id')
                    ->where('attachments.attachable_type', PresentationSlide::class);
            })
            ->leftJoin('products', 'edetailing_statistics.product_id', 'products.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->whereIntegerInRaw($table, $objects)
            ->whereIntegerInRaw('product_id', $products)
            ->whereBetween('start', [$from, $to])
            ->get();
    }
    private function detailing($product, $actuals)
    {
        $detailingCount = 0.0;
        $actualIds = $actuals->where('product_id', $product->id)->values()->pluck('id');
        $detailingCount = Statistic::whereIntegerInRaw('visit_id', $actualIds)->where('product_id', $product->id)->get()->sum('diffInSeconds');
        return round($detailingCount / 60, 2);
    }
    public function showData(Request $request)
    {
        $visit = $request->listFilter;
        $line = Line::find($visit['line']);
        $column = $request->column;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();

        $products = $line->products()->select('products.id', 'products.name')
            ->when(!empty($visit['products']), fn($q) => $q->whereIn("products.id", $visit['products']))->get();


        $fieldCalls = $products->map(function ($product) {
            return [
                'product_id' => $product->id,
                'name' => $product->name . ' ' . '(calls)',
                'detail' => $product->name . ' ' . '(detail_mins)',
            ];
        });

        if ($request->div != null) {
            $division = LineDivision::find($request->div);
            $actuals = $this->actualVisits($visit, $division, 'line_divisions.id', $from, $to, $products);
        } else {
            $user = User::find($request->user);
            $actuals = $this->actualVisits($visit, $user, 'users.id', $from, $to, $products);
        }

        $data = null;
        foreach ($fieldCalls as $field) {
            if ($field['name'] == $column) {
                $data = $actuals->where('product_id', $field['product_id'])->values()->map(function ($visit) {
                    return [
                        'id' => $visit->id,
                        'line' => $visit->line,
                        'division' => $visit->division,
                        'brick' => $visit->brick,
                        'account' => $visit->account,
                        'doctor' => $visit->doctor,
                        'product' => $visit->product,
                        'date' => $visit->date,
                    ];
                });
                return  $this->respond($data);
            }
            if ($field['detail'] == $column) {
                $actualIds = $actuals->where('product_id', $field['product_id'])->values()->pluck('id');
                $data = Statistic::whereIn('visit_id', $actualIds)->where('product_id', $field['product_id'])->get()->values()
                    ->map(function ($visit) use ($field) {
                        return [
                            'id' => $visit->id,
                            'product' => product::find($field['product_id'])->name,
                            'presentation' => Presentation::find($visit->presentation_id)->name,
                            'slide' => PresentationSlide::where('presentation_id', $visit->presentation_id)->first()?->attachment?->path ?? '',
                            'start' => Carbon::parse($visit->start)->toDateTimeString(),
                            'end' => Carbon::parse($visit->end)->toDateTimeString(),
                            'rate' => $visit->rate ?? '',
                            'difference' => round($visit->diffInSeconds / 60, 2),
                        ];
                    });;
                return  $this->respond($data);
            }
        }
    }
}
