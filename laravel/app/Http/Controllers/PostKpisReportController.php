<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Models\Kpi;
use App\Models\PostVisitKpi;
use App\Services\Enums\KPITypes;
use App\Services\PostVisitKpisService;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use PhpParser\Node\Stmt\Else_;

class PostKpisReportController extends ApiController
{
    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    public function filter(Request $request)
    {
        /**@var User authUser */
        $kpis = $request->kpisFilter;
        $from = Carbon::parse($kpis['fromDate'])->startOfDay();
        $to = Carbon::parse($kpis['toDate'])->endOfDay();
        $year = Carbon::parse($from)->format('Y');
        $period = CarbonPeriod::create($from, $to);
        $fieldResults = collect([]);

        $filtered = new Collection([]);
        $data = new Collection([]);
        $filtered = User::whereIntegerInRaw("id", !empty($kpis['users']) ? $kpis['users'] : $kpis['codes'])->get();

        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $kpis =
            $filtered->each(function ($user) use ($from, $to, &$data) {
                $data = $data->push($this->statistics($user, $from, $to));
            });
        $fields = collect($data[0]) // Take the first record
            ->except(['id', 'color']) // Remove unwanted keys
            ->keys() // Get the remaining keys
            ->toArray();
        $fieldResults = array_values(array_filter($fields, fn($key) => strpos($key, '(value)') !== false));
        return response()->json([
            'data' => $data,
            'fields' => $fields,
            'dates' => $dates,
            'fieldResults' => $fieldResults,
        ]);
    }

    private function statistics($user, $from, $to)
    {
        $divisionType = $user->divisions($from, $to)?->first()?->DivisionType;
        $type = $user->hasPosition() ? $user->positionDivision()->first()?->DivisionType : $divisionType;
        $line = $user->lines($from, $to)->first();
        $role = $user->hasPosition() ? $user->position() : $divisionType;
        $roleType = get_class($role);
        $data = collect([
            'id' => $user->id,
            'employee' => $user->fullname,
            'code' => $user->emp_code ?? '',
            'title' => $user->menuroles,
            'color' => $type?->color,
        ]);
        $kpisCount = 0;
        $count = 0;
        $tot = 0;
        if ($user->is_manager && $divisionType?->lastlevel != 4) {
            $mrExcellentKpi = Kpi::where('name', 'Mr Excellent')->first();
            $mrExcellentKpiRatio = $mrExcellentKpi?->ratios()
                ->where('kpi_ratios.line_id', $line?->id)
                ->where('kpi_ratios.roleable_id', $role?->id)
                ->where('kpi_ratios.roleable_type', $roleType)
                ->first();
            $mrExcellentPercents = $mrExcellentKpiRatio?->percents ?? collect([]);

            $belowUsers = $user->allBelowUsers($line, $from, $to)->where('is_manager', 0)->where('is_vacant', 0)->values();
            $getMrExcellent = $this->getMrExcellent($belowUsers, $from, $to, $mrExcellentKpi);
            foreach ($getMrExcellent as $item) {
                if ((float) $item['percent'] >= 4) {
                    $count++;
                }
            }

            $matchedValue = 0;

            $res = $belowUsers->count() > 0 ? round(($count / $belowUsers->count()) * 100, 2) : 0;
            foreach ($mrExcellentPercents as $range) {
                if ($res >= $range['from_percent'] && $res <= $range['to_percent']) {
                    $matchedValue = $range['value'];
                    break;
                }
            }
            $data = $data->put($mrExcellentKpi->name . ' ' . ' (ratio)', $mrExcellentKpiRatio?->ratio ?? '');
            $data = $data->put($mrExcellentKpi->name . ' ' . ' (act)', $count);
            $data = $data->put($mrExcellentKpi->name . ' ' . ' (res)', $res . '%' ?? '');
            $data = $data->put($mrExcellentKpi->name . ' ' . ' (value)', $matchedValue ?? '');
            $kpisCount++;
            $tot += $matchedValue;
        } else {
            $postedData = $this->getPostData($user, $line, $from, $to, $role, $roleType);
            $kpisCount += $postedData->pluck('kpi_id')->unique()->count();
            $postedData->each(function ($kpi)
            use (
                &$data,
                &$tot,
            ) {
                $tot +=  $kpi->value ?? 0;
                $result = ($kpi->percent * $kpi->ratio) / 100;
                $data = $data->put($kpi->kpi_name . ' ' . ' (ratio)', $kpi->ratio);
                $data = $data->put($kpi->kpi_name . ' ' . ' (act)', $kpi->percent);
                $data = $data->put($kpi->kpi_name . ' ' . ' (res)', $result . '%' ?? '');
                $data = $data->put($kpi->kpi_name . ' ' . ' (value)', $kpi->value ?? '');
            });
        }
        $data = $data->put('total', $kpisCount > 0 ? ceil($tot / $kpisCount) : 0);

        return $data;
    }


    private function getPostData($user, $line, $from, $to, $role, $roleType)
    {
        return PostVisitKpi::select(
            'kpis.id as kpi_id',
            'kpis.name as kpi_name',
            'users.fullname as employee',
            'post_visit_kpis.percent as percent',
            'kpi_ratios.ratio as ratio',
            'kpi_percents.value as value',
        )
            ->join('users', function ($join) use ($user) {
                $join->on('post_visit_kpis.user_id', 'users.id')->where('user_id', $user->id);
            })
            ->join('kpis', 'post_visit_kpis.kpi_id', 'kpis.id')
            ->join('kpi_ratios', function ($join) use ($line, $role, $roleType) {
                $join->on('kpi_ratios.kpi_id', 'kpis.id')
                    ->where('kpi_ratios.line_id', $line?->id)
                    ->where('kpi_ratios.roleable_id', $role?->id)
                    ->where('kpi_ratios.roleable_type', $roleType);
            })
            ->leftJoin('kpi_percents', function ($join) {
                $join->on('kpi_ratios.id', '=', 'kpi_percents.kpi_ratio_id')
                    ->whereRaw('crm_post_visit_kpis.percent BETWEEN crm_kpi_percents.`from_percent` AND crm_kpi_percents.`to_percent`');
            })
            ->whereBetween('date', [$from, $to])->get();
    }

    private function getMrExcellent($belowUsers, $from, $to, $kpi)
    {
        $postedData = PostVisitKpi::select('post_visit_kpis.percent as percent')
            ->whereIn('user_id', $belowUsers->pluck('id')->toArray())
            ->where('post_visit_kpis.kpi_id', $kpi->id)
            ->whereBetween('date', [$from, $to])
            ->get();
        return $postedData;
    }

    public function post(Request $request)
    {
        $data = $request->data;
        $filters = $request->filters;
        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $mrExcellentKpi = Kpi::where('name', 'MR Excellent')->first();
        $period = CarbonPeriod::create($from, '1 month', $to);
        $formattedData = array_map(function ($item) {
            return [
                'id' => $item['id'],
                'mr_excellent' => ceil($item['total']),
            ];
        }, $data);
        $this->postVisitKpisService->post(KPITypes::MR_EXCELLENT, $formattedData, $period, $mrExcellentKpi);
        return $this->respondSuccess();
    }
}
