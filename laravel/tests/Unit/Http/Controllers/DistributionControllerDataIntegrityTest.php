<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\DistributionController;
use App\Services\Sales\Ceiling\DistributionService;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionStrategyFactory;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Support\Collection;
use Mockery;
use PHPUnit\Framework\TestCase;

/**
 * Test data integrity for DistributionController
 * 
 * This test specifically addresses the bug where sales totals before and after
 * distribution processing are not matching for PRIVATE_PHARMACY distribution type.
 */
class DistributionControllerDataIntegrityTest extends TestCase
{
    private DistributionService $distributionService;
    private DistributionStrategyFactory $strategyFactory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->distributionService = Mockery::mock(DistributionService::class);
        $this->strategyFactory = Mockery::mock(DistributionStrategyFactory::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test data integrity calculation logic for PRIVATE_PHARMACY distribution
     */
    public function test_private_pharmacy_distribution_maintains_sales_totals(): void
    {
        // Arrange - Create test data that reproduces the issue
        $ceilingSales = $this->createTestCeilingSalesData();
        $expectedTotalBefore = $ceilingSales->sum('number_of_values'); // 15000.00

        // Test the core business logic directly using reflection
        $controller = new DistributionController($this->distributionService, $this->strategyFactory);

        // Use reflection to test the private calculateTotalSalesValue method
        $reflection = new \ReflectionClass($controller);
        $calculateMethod = $reflection->getMethod('calculateTotalSalesValue');
        $calculateMethod->setAccessible(true);

        // Act - Test the calculation method directly
        $totalBefore = $calculateMethod->invoke($controller, $ceilingSales);

        // Assert - Verify the calculation is correct
        $this->assertEquals($expectedTotalBefore, $totalBefore);
        $this->assertEquals(15000.00, $totalBefore);

        // Test that the calculation is consistent
        $totalBefore2 = $calculateMethod->invoke($controller, $ceilingSales);
        $this->assertEquals($totalBefore, $totalBefore2);

        // Test with empty collection
        $emptyCollection = new Collection();
        $emptyTotal = $calculateMethod->invoke($controller, $emptyCollection);
        $this->assertEquals(0.0, $emptyTotal);
    }

    /**
     * Test calculation with empty ceiling sales collection
     */
    public function test_empty_ceiling_sales_calculation(): void
    {
        // Arrange
        $emptyCeilingSales = new Collection();

        // Test the core business logic directly using reflection
        $controller = new DistributionController($this->distributionService, $this->strategyFactory);

        // Use reflection to test the private calculateTotalSalesValue method
        $reflection = new \ReflectionClass($controller);
        $calculateMethod = $reflection->getMethod('calculateTotalSalesValue');
        $calculateMethod->setAccessible(true);

        // Act
        $total = $calculateMethod->invoke($controller, $emptyCeilingSales);

        // Assert
        $this->assertEquals(0.0, $total);
    }

    /**
     * Create test ceiling sales data that reproduces the issue
     */
    private function createTestCeilingSalesData(): Collection
    {
        return new Collection([
            (object) [
                'id' => 1,
                'sale_ids' => '1,2,3',
                'number_of_units' => 150, // Exceeds limit of 100
                'number_of_values' => 7500.00,
                'number_of_bonus' => 15,
                'limit' => 100,
                'negative_limit' => -50,
                'distributor_id' => 1,
                'date' => '2025-01-15',
                'mapping_id' => 1
            ],
            (object) [
                'id' => 2,
                'sale_ids' => '4,5',
                'number_of_units' => 120, // Exceeds limit of 80
                'number_of_values' => 6000.00,
                'number_of_bonus' => 12,
                'limit' => 80,
                'negative_limit' => -40,
                'distributor_id' => 2,
                'date' => '2025-01-20',
                'mapping_id' => 2
            ],
            (object) [
                'id' => 3,
                'sale_ids' => '6',
                'number_of_units' => 90, // Exceeds limit of 60
                'number_of_values' => 1500.00,
                'number_of_bonus' => 9,
                'limit' => 60,
                'negative_limit' => -30,
                'distributor_id' => 3,
                'date' => '2025-01-25',
                'mapping_id' => 3
            ]
        ]);
    }

    /**
     * Test DistributionType enum validation
     */
    public function test_distribution_type_validation(): void
    {
        // Test valid distribution types
        $validTypes = [
            DistributionType::PRIVATE_PHARMACY->value,
            DistributionType::STORES->value,
            DistributionType::LOCAL_CHAINS->value
        ];

        foreach ($validTypes as $type) {
            $distributionType = DistributionType::from($type);
            $this->assertInstanceOf(DistributionType::class, $distributionType);
        }

        // Test invalid distribution type
        $this->expectException(\ValueError::class);
        DistributionType::from(999);
    }
}
