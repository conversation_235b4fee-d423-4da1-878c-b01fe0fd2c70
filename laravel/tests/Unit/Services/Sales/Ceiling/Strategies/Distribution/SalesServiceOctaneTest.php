<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution;

use App\Services\SalesService;
use App\Services\Enums\SaleDistribution;
use Illuminate\Support\Facades\{Cache, DB};
use Tests\TestCase;
use ReflectionClass;

/**
 * Test class for SalesService Octane compatibility
 *
 * Tests specifically focused on SalesService state management, caching behavior,
 * and memory usage patterns that could cause issues in Laravel Octane.
 *
 * @covers \App\Services\SalesService
 */
class SalesServiceOctaneTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Clear cache before each test
        Cache::flush();
    }

    protected function tearDown(): void
    {
        // Clear cache after each test
        Cache::flush();

        // Force garbage collection
        gc_collect_cycles();

        parent::tearDown();
    }

    /**
     * Test that SalesService instances have isolated state
     */
    public function test_sales_service_instance_isolation(): void
    {
        // Arrange - Create multiple instances
        $service1 = SalesService::make(SaleDistribution::NORMAL);
        $service2 = SalesService::make(SaleDistribution::DIRECT);
        $service3 = SalesService::make(SaleDistribution::NORMAL);

        // Act - Modify state on each service differently
        $service1->addSelect('field_1');
        $service1->addSelect('field_2');
        $service1->forNegativeCeiling();

        $service2->addSelect('field_3');
        // service2 does not call forNegativeCeiling()

        $service3->addSelect('field_4');
        $service3->addSelect('field_5');
        $service3->forNegativeCeiling();

        // Assert - Each service should have its own state
        $this->assertServiceState($service1, ['field_1', 'field_2'], true);
        $this->assertServiceState($service2, ['field_3'], false);
        $this->assertServiceState($service3, ['field_4', 'field_5'], true);
    }

    /**
     * Test cache key generation uniqueness
     */
    public function test_cache_key_generation_uniqueness(): void
    {
        // Arrange
        $service1 = SalesService::make(SaleDistribution::NORMAL);
        $service2 = SalesService::make(SaleDistribution::DIRECT);
        $service3 = SalesService::make(SaleDistribution::NORMAL);

        // Modify services to create different states
        $service1->addSelect('custom_field_1');
        $service2->addSelect('custom_field_2');
        $service3->forNegativeCeiling();

        // Act - Generate cache keys using reflection with service state
        $cacheKeys = [];
        $services = [$service1, $service2, $service3];

        foreach ($services as $index => $service) {
            $reflection = new ReflectionClass($service);
            $method = $reflection->getMethod('generateCacheKey');
            $method->setAccessible(true);

            // Get service state to include in cache key generation
            $state = $this->getServiceState($service);
            $params = ['testFunction', [$state['isNegativeCeiling'], ...$state['selections'], 'param1', 'param2']];
            $cacheKeys[$index] = $method->invoke($service, ...$params);
        }

        // Assert - All cache keys should be different
        $this->assertCount(3, array_unique($cacheKeys),
            'Cache keys should be unique for different service states');
    }

    /**
     * Test that cache doesn't leak between different service configurations
     */
    public function test_cache_isolation_between_service_configurations(): void
    {
        // Arrange - Test cache key generation for different service configurations
        $normalService = SalesService::make(SaleDistribution::NORMAL);
        $directService = SalesService::make(SaleDistribution::DIRECT);
        $modifiedNormalService = SalesService::make(SaleDistribution::NORMAL);
        $modifiedNormalService->addSelect('extra_field');

        // Act - Generate cache keys for each service configuration
        $cacheKeys = [];
        $services = [$normalService, $directService, $modifiedNormalService];

        foreach ($services as $service) {
            $reflection = new ReflectionClass($service);
            $method = $reflection->getMethod('generateCacheKey');
            $method->setAccessible(true);

            // Get service state to include in cache key generation
            $state = $this->getServiceState($service);
            $distributionProperty = $reflection->getProperty('saleDistribution');
            $distributionProperty->setAccessible(true);
            $saleDistribution = $distributionProperty->getValue($service);

            $params = ['getRatiosForDistribution', [$saleDistribution, $state['isNegativeCeiling'], ...$state['selections'], '2024-01-01', 1, [1, 2, 3]]];
            $cacheKeys[] = $method->invoke($service, ...$params);
        }

        // Assert - Should have different cache keys
        $this->assertCount(3, $cacheKeys);
        $this->assertCount(3, array_unique($cacheKeys),
            'Different service configurations should produce different cache keys');
    }

    /**
     * Test memory usage with repeated service creation
     */
    public function test_memory_usage_with_repeated_service_creation(): void
    {
        // Arrange
        $initialMemory = memory_get_usage(true);
        $memoryReadings = [];

        // Act - Create many service instances
        for ($i = 0; $i < 100; $i++) {
            $service = SalesService::make(SaleDistribution::NORMAL);
            $service->addSelect("field_$i");

            // Take memory reading every 20 iterations
            if ($i % 20 === 0) {
                gc_collect_cycles();
                $memoryReadings[] = memory_get_usage(true);
            }

            // Explicitly unset to help garbage collection
            unset($service);
        }

        // Assert - Memory should not grow excessively
        $finalMemory = end($memoryReadings);
        $memoryGrowth = $finalMemory - $initialMemory;

        $this->assertLessThan(10 * 1024 * 1024, $memoryGrowth,
            "Memory grew by " . number_format($memoryGrowth / 1024 / 1024, 2) . "MB, indicating potential memory leak");
    }

    /**
     * Test that SQL mode changes don't persist between service calls
     */
    public function test_sql_mode_isolation(): void
    {
        // Arrange
        $sqlModeChanges = [];

        // Mock DB facade completely
        DB::partialMock()
            ->shouldReceive('statement')
            ->withArgs(function ($sql) use (&$sqlModeChanges) {
                if (str_contains($sql, 'sql_mode')) {
                    $sqlModeChanges[] = $sql;
                }
                return true;
            })
            ->andReturn(true);

        DB::shouldReceive('raw')->andReturnUsing(function ($sql) {
            return new \Illuminate\Database\Query\Expression($sql);
        });

        // Act - Create multiple services and simulate SQL mode changes
        for ($i = 0; $i < 3; $i++) {
            SalesService::make(SaleDistribution::NORMAL);

            // Simulate the SQL mode change that happens in getRatiosForDistribution
            DB::statement("set sql_mode=''");
        }

        // Assert - SQL mode should be changed for each service call
        $this->assertCount(3, $sqlModeChanges);
        foreach ($sqlModeChanges as $change) {
            $this->assertEquals("set sql_mode=''", $change);
        }
    }

    /**
     * Test service state doesn't leak between requests (simulated)
     */
    public function test_service_state_request_isolation(): void
    {
        // Simulate multiple "requests" by creating and using services
        $requestResults = [];

        for ($requestId = 0; $requestId < 5; $requestId++) {
            // Simulate request-specific service usage
            $service = SalesService::make(SaleDistribution::NORMAL);

            // Each "request" modifies the service differently
            for ($i = 0; $i < $requestId + 1; $i++) {
                $service->addSelect("request_{$requestId}_field_{$i}");
            }

            if ($requestId % 2 === 0) {
                $service->forNegativeCeiling();
            }

            // Store the service state for verification
            $requestResults[$requestId] = $this->getServiceState($service);
        }

        // Assert - Each "request" should have isolated state
        for ($requestId = 0; $requestId < 5; $requestId++) {
            $state = $requestResults[$requestId];

            // Verify expected number of custom selections
            $customSelections = array_filter($state['selections'], function ($selection) use ($requestId) {
                // Handle both string selections and DB::raw expressions
                if (is_string($selection)) {
                    $selectionString = $selection;
                } elseif ($selection instanceof \Illuminate\Database\Query\Expression) {
                    $selectionString = $selection->getValue(\Illuminate\Support\Facades\DB::connection()->getQueryGrammar());
                } else {
                    $selectionString = '';
                }
                return str_contains($selectionString, "request_{$requestId}_field_");
            });

            $this->assertCount($requestId + 1, $customSelections,
                "Request $requestId should have " . ($requestId + 1) . " custom selections");

            // Verify negative ceiling state
            $expectedNegativeCeiling = ($requestId % 2 === 0);
            $this->assertEquals($expectedNegativeCeiling, $state['isNegativeCeiling'],
                "Request $requestId should have negative ceiling: " . ($expectedNegativeCeiling ? 'true' : 'false'));
        }
    }

    /**
     * Helper method to assert service state
     */
    private function assertServiceState(SalesService $service, array $expectedCustomFields, bool $expectedNegativeCeiling): void
    {
        $state = $this->getServiceState($service);

        foreach ($expectedCustomFields as $field) {
            $this->assertContains($field, $state['selections'], "Service should contain field: $field");
        }

        $this->assertEquals($expectedNegativeCeiling, $state['isNegativeCeiling'],
            "Service negative ceiling state should be: " . ($expectedNegativeCeiling ? 'true' : 'false'));
    }

    /**
     * Helper method to get service state using reflection
     */
    private function getServiceState(SalesService $service): array
    {
        $reflection = new ReflectionClass($service);

        $selectionsProperty = $reflection->getProperty('selections');
        $selectionsProperty->setAccessible(true);

        $negativeCeilingProperty = $reflection->getProperty('isNegativeCeiling');
        $negativeCeilingProperty->setAccessible(true);

        return [
            'selections' => $selectionsProperty->getValue($service),
            'isNegativeCeiling' => $negativeCeilingProperty->getValue($service)
        ];
    }
}
