<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Services;

use App\Services\Sales\Ceiling\Strategies\Distribution\Services\LimitCalculator;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\LimitCalculatorInterface;
use Tests\TestCase;

/**
 * Test class for LimitCalculator
 * 
 * Tests the limit calculation service that determines limits for ceiling sales
 * and checks if sales exceed their limits
 * 
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Services\LimitCalculator
 */
class LimitCalculatorTest extends TestCase
{
    private LimitCalculator $limitCalculator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->limitCalculator = new LimitCalculator();
    }

    /**
     * Test calculate limit with positive units
     */
    public function test_calculate_limit_with_positive_units(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 150,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->calculateLimit($ceilingSale);

        // Assert
        $this->assertEquals(100, $result);
    }

    /**
     * Test calculate limit with negative units
     */
    public function test_calculate_limit_with_negative_units(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->calculateLimit($ceilingSale);

        // Assert
        $this->assertEquals(-50, $result);
    }

    /**
     * Test calculate limit with zero units
     */
    public function test_calculate_limit_with_zero_units(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 0,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->calculateLimit($ceilingSale);

        // Assert
        $this->assertEquals(0, $result);
    }

    /**
     * Test exceeds limit with positive units that exceed limit
     */
    public function test_exceeds_limit_positive_units_exceeds(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 150,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test exceeds limit with positive units that do not exceed limit
     */
    public function test_exceeds_limit_positive_units_does_not_exceed(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 80,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test exceeds limit with positive units equal to limit
     */
    public function test_exceeds_limit_positive_units_equal_to_limit(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 100,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test exceeds limit with negative units that exceed limit
     */
    public function test_exceeds_limit_negative_units_exceeds(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertTrue($result); // -75 < -50
    }

    /**
     * Test exceeds limit with negative units that do not exceed limit
     */
    public function test_exceeds_limit_negative_units_does_not_exceed(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -30,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertFalse($result); // -30 > -50
    }

    /**
     * Test exceeds limit with negative units equal to limit
     */
    public function test_exceeds_limit_negative_units_equal_to_limit(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -50,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test exceeds limit with zero units
     */
    public function test_exceeds_limit_zero_units(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 0,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test with edge case: very large positive numbers
     */
    public function test_calculate_limit_with_large_positive_numbers(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 999999,
            'limit' => 500000,
            'negative_limit' => -100000
        ];

        // Act
        $limitResult = $this->limitCalculator->calculateLimit($ceilingSale);
        $exceedsResult = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertEquals(500000, $limitResult);
        $this->assertTrue($exceedsResult);
    }

    /**
     * Test with edge case: very large negative numbers
     */
    public function test_calculate_limit_with_large_negative_numbers(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -999999,
            'limit' => 500000,
            'negative_limit' => -100000
        ];

        // Act
        $limitResult = $this->limitCalculator->calculateLimit($ceilingSale);
        $exceedsResult = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertEquals(-100000, $limitResult);
        $this->assertTrue($exceedsResult);
    }

    /**
     * Test with decimal numbers
     */
    public function test_calculate_limit_with_decimal_numbers(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 150.75,
            'limit' => 100.5,
            'negative_limit' => -50.25
        ];

        // Act
        $limitResult = $this->limitCalculator->calculateLimit($ceilingSale);
        $exceedsResult = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertEquals(100.5, $limitResult);
        $this->assertTrue($exceedsResult);
    }

    /**
     * Test calculate limit with null positive limit (LEFT JOIN scenario for STORES)
     */
    public function test_calculate_limit_with_null_positive_limit(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 150,
            'limit' => null,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->calculateLimit($ceilingSale);

        // Assert
        $this->assertEquals(0, $result); // Should default to 0 when limit is null
    }

    /**
     * Test calculate limit with null negative limit (LEFT JOIN scenario for STORES)
     */
    public function test_calculate_limit_with_null_negative_limit(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => null
        ];

        // Act
        $result = $this->limitCalculator->calculateLimit($ceilingSale);

        // Assert
        $this->assertEquals(0, $result); // Should default to 0 when negative_limit is null
    }

    /**
     * Test exceeds limit with null positive limit (LEFT JOIN scenario for STORES)
     */
    public function test_exceeds_limit_with_null_positive_limit(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => 150,
            'limit' => null,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertTrue($result); // Should return true when limit is null (allows processing)
    }

    /**
     * Test exceeds limit with null negative limit (LEFT JOIN scenario for STORES)
     */
    public function test_exceeds_limit_with_null_negative_limit(): void
    {
        // Arrange
        $ceilingSale = (object) [
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => null
        ];

        // Act
        $result = $this->limitCalculator->exceedsLimit($ceilingSale);

        // Assert
        $this->assertTrue($result); // Should return true when negative_limit is null (allows processing)
    }

    /**
     * Test exceeds limit with both limits null (LEFT JOIN scenario for STORES)
     */
    public function test_exceeds_limit_with_both_limits_null(): void
    {
        // Arrange - positive units
        $ceilingSalePositive = (object) [
            'number_of_units' => 150,
            'limit' => null,
            'negative_limit' => null
        ];

        // Arrange - negative units
        $ceilingSaleNegative = (object) [
            'number_of_units' => -75,
            'limit' => null,
            'negative_limit' => null
        ];

        // Act
        $resultPositive = $this->limitCalculator->exceedsLimit($ceilingSalePositive);
        $resultNegative = $this->limitCalculator->exceedsLimit($ceilingSaleNegative);

        // Assert
        $this->assertTrue($resultPositive); // Should return true for positive units with null limit
        $this->assertTrue($resultNegative); // Should return true for negative units with null negative_limit
    }

    /**
     * Test that the class implements LimitCalculatorInterface
     */
    public function test_implements_limit_calculator_interface(): void
    {
        $this->assertInstanceOf(LimitCalculatorInterface::class, $this->limitCalculator);
    }

    /**
     * Test interface method signatures are correctly implemented
     */
    public function test_interface_methods_exist(): void
    {
        $this->assertTrue(method_exists($this->limitCalculator, 'calculateLimit'));
        $this->assertTrue(method_exists($this->limitCalculator, 'exceedsLimit'));
    }
}
