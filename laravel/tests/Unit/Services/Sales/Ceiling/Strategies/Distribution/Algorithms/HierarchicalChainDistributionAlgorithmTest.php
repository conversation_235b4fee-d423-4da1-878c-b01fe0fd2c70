<?php

namespace Tests\Unit\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\HierarchicalChainDistributionAlgorithm;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\HierarchicalDistributionService;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\SalesService;
use Tests\TestCase;
use Mockery;

/**
 * Test class for HierarchicalChainDistributionAlgorithm
 *
 * Tests the hierarchical chain distribution algorithm for local chain pharmacies
 * that uses a hierarchical 100-distribution for current district approach
 *
 * @covers \App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\HierarchicalChainDistributionAlgorithm
 */
class HierarchicalChainDistributionAlgorithmTest extends TestCase
{
    private HierarchicalChainDistributionAlgorithm $algorithm;
    private SalesService $salesService;
    private SaleDetailFactory $saleDetailFactory;
    private HierarchicalDistributionService $hierarchicalDistributionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->salesService = Mockery::mock(SalesService::class);
        $this->saleDetailFactory = Mockery::mock(SaleDetailFactory::class);
        $this->hierarchicalDistributionService = Mockery::mock(HierarchicalDistributionService::class);

        $this->algorithm = new HierarchicalChainDistributionAlgorithm(
            $this->salesService,
            $this->saleDetailFactory,
            $this->hierarchicalDistributionService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful distribution with original sale
     */
    public function test_distribute_excess_sale_success_with_original_sale(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = $this->createMockOriginalSale();
        $salesContributionBaseOn = [1, 2, 3];

        $distributionRatios = collect([
            (object)['div_id' => 10, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 0.6],
            (object)['div_id' => 11, 'line_id' => 2, 'brick_id' => 2, 'percentage' => 0.4],
        ]);

        $expectedDetails = [
            ['sale_id' => 1, 'div_id' => 10, 'quantity' => 60],
            ['sale_id' => 1, 'div_id' => 11, 'quantity' => 40],
        ];

        // Mock HierarchicalDistributionService
        $this->hierarchicalDistributionService
            ->shouldReceive('getDeepestDescendantIds')
            ->once()
            ->with($originalSale)
            ->andReturn([10, 11]);

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn, [10, 11])
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $distributionRatios->toArray())
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test distribution without original sale (returns empty collection)
     */
    public function test_distribute_excess_sale_without_original_sale(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = null;
        $salesContributionBaseOn = [1, 2, 3];

        $emptyRatios = collect([]);
        $expectedDetails = [];

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $emptyRatios->toArray())
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test calculate excess quantity with positive units
     */
    public function test_calculate_excess_quantity_positive_units(): void
    {
        // Arrange
        $ceilingSale = (object)[
            'number_of_units' => 150,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(50, $result); // 150 - 100
    }

    /**
     * Test calculate excess quantity with negative units
     */
    public function test_calculate_excess_quantity_negative_units(): void
    {
        // Arrange
        $ceilingSale = (object)[
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(-25, $result); // -75 - (-50)
    }

    /**
     * Test calculate excess quantity with zero units
     */
    public function test_calculate_excess_quantity_zero_units(): void
    {
        // Arrange
        $ceilingSale = (object)[
            'number_of_units' => 0,
            'limit' => 100,
            'negative_limit' => -50
        ];

        // Act
        $result = $this->algorithm->calculateExcessQuantity($ceilingSale);

        // Assert
        $this->assertEquals(0, $result); // 0 - 0 (limit is 0 when number_of_units is 0)
    }

    /**
     * Test calculate excess quantity with null limits (LEFT JOIN scenario for STORES)
     */
    public function test_calculate_excess_quantity_with_null_limits(): void
    {
        // Arrange - positive units with null limit
        $ceilingSalePositive = (object)[
            'number_of_units' => 150,
            'limit' => null,
            'negative_limit' => -50
        ];

        // Arrange - negative units with null negative_limit
        $ceilingSaleNegative = (object)[
            'number_of_units' => -75,
            'limit' => 100,
            'negative_limit' => null
        ];

        // Act
        $resultPositive = $this->algorithm->calculateExcessQuantity($ceilingSalePositive);
        $resultNegative = $this->algorithm->calculateExcessQuantity($ceilingSaleNegative);

        // Assert
        $this->assertEquals(150, $resultPositive); // 150 - 0 = 150 (null limit defaults to 0)
        $this->assertEquals(-75, $resultNegative); // -75 - 0 = -75 (null negative_limit defaults to 0)
    }

    /**
     * Test calculate excess quantity with both limits null (LEFT JOIN scenario for STORES)
     */
    public function test_calculate_excess_quantity_with_both_limits_null(): void
    {
        // Arrange - positive units with both limits null
        $ceilingSalePositive = (object)[
            'number_of_units' => 200,
            'limit' => null,
            'negative_limit' => null
        ];

        // Arrange - negative units with both limits null
        $ceilingSaleNegative = (object)[
            'number_of_units' => -100,
            'limit' => null,
            'negative_limit' => null
        ];

        // Act
        $resultPositive = $this->algorithm->calculateExcessQuantity($ceilingSalePositive);
        $resultNegative = $this->algorithm->calculateExcessQuantity($ceilingSaleNegative);

        // Assert
        $this->assertEquals(200, $resultPositive); // 200 - 0 = 200 (null limit defaults to 0)
        $this->assertEquals(-100, $resultNegative); // -100 - 0 = -100 (null negative_limit defaults to 0)
    }

    /**
     * Test distribution failure when insertion fails
     */
    public function test_distribute_excess_sale_insertion_failure(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = $this->createMockOriginalSale();
        $salesContributionBaseOn = [1, 2, 3];

        $distributionRatios = collect([
            (object)['div_id' => 10, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 1.0],
        ]);

        $expectedDetails = [
            ['sale_id' => 1, 'div_id' => 10, 'quantity' => 100],
        ];

        // Mock HierarchicalDistributionService
        $this->hierarchicalDistributionService
            ->shouldReceive('getDeepestDescendantIds')
            ->once()
            ->with($originalSale)
            ->andReturn([10]);

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(false);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test distribution with complex hierarchy
     */
    public function test_distribute_excess_sale_with_complex_hierarchy(): void
    {
        // Arrange
        $sale = $this->createMockSale();
        $originalSale = $this->createMockComplexOriginalSale();
        $salesContributionBaseOn = [1, 2, 3];

        $distributionRatios = collect([
            (object)['div_id' => 20, 'line_id' => 1, 'brick_id' => 1, 'percentage' => 0.3],
            (object)['div_id' => 21, 'line_id' => 2, 'brick_id' => 2, 'percentage' => 0.3],
            (object)['div_id' => 22, 'line_id' => 3, 'brick_id' => 3, 'percentage' => 0.4],
        ]);

        $expectedDetails = [
            ['sale_id' => 1, 'div_id' => 20, 'quantity' => 30],
            ['sale_id' => 1, 'div_id' => 21, 'quantity' => 30],
            ['sale_id' => 1, 'div_id' => 22, 'quantity' => 40],
        ];

        // Mock HierarchicalDistributionService
        $this->hierarchicalDistributionService
            ->shouldReceive('getDeepestDescendantIds')
            ->once()
            ->with($originalSale)
            ->andReturn([20, 21, 22]);

        // Mock SalesService
        $this->salesService
            ->shouldReceive('getRatiosForDistribution')
            ->once()
            ->with($sale->date, $sale->product_id, $salesContributionBaseOn, [20, 21, 22])
            ->andReturn($distributionRatios);

        // Mock SaleDetailFactory
        $this->saleDetailFactory
            ->shouldReceive('createDetailsFromRatios')
            ->once()
            ->with($sale, $distributionRatios->toArray())
            ->andReturn($expectedDetails);

        $this->saleDetailFactory
            ->shouldReceive('insertDetails')
            ->once()
            ->with($expectedDetails)
            ->andReturn(true);

        // Act
        $result = $this->algorithm->distributeExcessSale($sale, $salesContributionBaseOn, $originalSale);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test that algorithm implements ExcessDistributorInterface
     */
    public function test_implements_excess_distributor_interface(): void
    {
        $this->assertInstanceOf(
            \App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface::class,
            $this->algorithm
        );
    }

    /**
     * Create a mock Sale object for testing
     */
    private function createMockSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);

        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 1,
                'date' => '2023-01-01',
                'product_id' => 1,
                'quantity' => 100,
                'value' => 1000,
                'bonus' => 50,
                default => null,
            };
        });

        // Allow other common Eloquent methods that might be called
        $sale->shouldReceive('load')->andReturnSelf();
        $sale->shouldReceive('mappings')->andReturnSelf();
        $sale->shouldReceive('attach')->andReturn(true);

        $sale->id = 1;
        $sale->date = '2023-01-01';
        $sale->product_id = 1;
        $sale->quantity = 100;
        $sale->value = 1000;
        $sale->bonus = 50;

        return $sale;
    }

    /**
     * Create a mock original Sale with details for testing
     */
    private function createMockOriginalSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 2,
                default => null,
            };
        });
        $sale->id = 2;

        // Mock sale details
        $details = collect([
            (object)['div_id' => 5],
            (object)['div_id' => 6],
        ]);
        $sale->details = $details;

        // Mock LineDivision::whereIn query
//        $lineDivisionMock = Mockery::mock('alias:' . LineDivision::class);
//        $lineDivisionMock
//            ->shouldReceive('whereIn')
//            ->with('id', [5, 6])
//            ->andReturnSelf();
//
//        $lineDivisionMock
//            ->shouldReceive('get')
//            ->andReturn(collect([
//                $this->createMockLineDivision([10, 11]),
//            ]));

        return $sale;
    }

    /**
     * Create a mock original Sale with complex hierarchy for testing
     */
    private function createMockComplexOriginalSale(): Sale
    {
        $sale = Mockery::mock(Sale::class);
        $sale->shouldReceive('setAttribute')->andReturnSelf();
        $sale->shouldReceive('getAttribute')->andReturnUsing(function ($key) {
            return match ($key) {
                'id' => 3,
                default => null,
            };
        });
        $sale->id = 3;

        // Mock sale details with more divisions
        $details = collect([
            (object)['div_id' => 7],
            (object)['div_id' => 8],
            (object)['div_id' => 9],
        ]);
        $sale->details = $details;

        return $sale;
    }

}
