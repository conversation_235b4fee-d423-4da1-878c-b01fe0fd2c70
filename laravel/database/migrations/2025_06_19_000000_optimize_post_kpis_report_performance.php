<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Optimize post_visit_kpis table - main performance bottleneck
        Schema::table('post_visit_kpis', function (Blueprint $table) {
            // Primary composite index for most common queries in PostKpisReportController
            $table->index(['user_id', 'kpi_id', 'date'], 'idx_post_kpis_user_kpi_date');
            
            // Index for date range queries (whereBetween date)
            $table->index(['date', 'user_id'], 'idx_post_kpis_date_user');
            
            // Index for KPI-specific queries
            $table->index(['kpi_id', 'user_id'], 'idx_post_kpis_kpi_user');
            
            // Index for line-based filtering
            $table->index(['line_id', 'date'], 'idx_post_kpis_line_date');
            
            // Index for percent-based calculations
            $table->index(['percent', 'kpi_id'], 'idx_post_kpis_percent_kpi');
        });

        // Optimize kpis table
        Schema::table('kpis', function (Blueprint $table) {
            // Index for name-based lookups (Mr Excellent, etc.)
            $table->index(['name', 'deleted_at'], 'idx_kpis_name_deleted');
            
            // Index for soft deletes
            $table->index(['deleted_at'], 'idx_kpis_deleted');
        });

        // Optimize kpi_ratios table - heavily used in joins
        Schema::table('kpi_ratios', function (Blueprint $table) {
            // Primary composite index for role-based queries
            $table->index(['kpi_id', 'line_id', 'roleable_id', 'roleable_type'], 'idx_kpi_ratios_composite');
            
            // Index for line-specific queries
            $table->index(['line_id', 'kpi_id'], 'idx_kpi_ratios_line_kpi');
            
            // Index for polymorphic relationships
            $table->index(['roleable_id', 'roleable_type'], 'idx_kpi_ratios_polymorphic');
            
            // Index for KPI lookups
            $table->index(['kpi_id'], 'idx_kpi_ratios_kpi');
        });

        // Optimize kpi_percents table - used in complex joins with BETWEEN clauses
        Schema::table('kpi_percents', function (Blueprint $table) {
            // Index for ratio-based lookups
            $table->index(['kpi_ratio_id'], 'idx_kpi_percents_ratio');
            
            // Composite index for percentage range queries
            $table->index(['kpi_ratio_id', 'from_percent', 'to_percent'], 'idx_kpi_percents_range');
            
            // Index for value lookups
            $table->index(['value'], 'idx_kpi_percents_value');
        });

        // Optimize users table for user filtering in reports
        Schema::table('users', function (Blueprint $table) {
            // Index for user filtering in reports (is_manager is an accessor, not a column)
            $table->index(['deleted_at', 'is_vacant'], 'idx_users_active_status');

            // Index for status-based filtering
            $table->index(['status', 'deleted_at'], 'idx_users_status');
        });

        // Optimize line_users table for user-line relationships
        if (Schema::hasTable('line_users')) {
            Schema::table('line_users', function (Blueprint $table) {
                // Index for user-line lookups with date ranges
                $table->index(['user_id', 'line_id', 'from_date', 'to_date'], 'idx_line_users_composite_kpi');
                
                // Index for date-based filtering
                $table->index(['from_date', 'to_date'], 'idx_line_users_dates_kpi');
            });
        }

        // Optimize line_divisions table for position queries
        if (Schema::hasTable('line_divisions')) {
            Schema::table('line_divisions', function (Blueprint $table) {
                // Index for division type lookups
                $table->index(['division_type_id', 'deleted_at'], 'idx_line_divisions_type_kpi');

                // Index for line-based queries
                $table->index(['line_id', 'deleted_at'], 'idx_line_divisions_line_kpi');
            });
        }

        // Optimize division_types table for manager status determination
        if (Schema::hasTable('division_types')) {
            Schema::table('division_types', function (Blueprint $table) {
                // Index for last_level queries (determines manager status)
                $table->index(['last_level', 'deleted_at'], 'idx_division_types_last_level_kpi');
            });
        }

        // Optimize line_users_divisions table for user-division relationships
        if (Schema::hasTable('line_users_divisions')) {
            Schema::table('line_users_divisions', function (Blueprint $table) {
                // Index for user-division lookups with date ranges
                $table->index(['user_id', 'line_division_id', 'deleted_at'], 'idx_line_users_divisions_kpi');

                // Index for date-based filtering
                $table->index(['from_date', 'to_date', 'deleted_at'], 'idx_line_users_divisions_dates_kpi');
            });
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        if (Schema::hasTable('line_divisions')) {
            Schema::table('line_divisions', function (Blueprint $table) {
                $table->dropIndex('idx_line_divisions_type_kpi');
                $table->dropIndex('idx_line_divisions_line_kpi');
            });
        }

        if (Schema::hasTable('line_users_divisions')) {
            Schema::table('line_users_divisions', function (Blueprint $table) {
                $table->dropIndex('idx_line_users_divisions_kpi');
                $table->dropIndex('idx_line_users_divisions_dates_kpi');
            });
        }

        if (Schema::hasTable('division_types')) {
            Schema::table('division_types', function (Blueprint $table) {
                $table->dropIndex('idx_division_types_last_level_kpi');
            });
        }

        if (Schema::hasTable('line_users')) {
            Schema::table('line_users', function (Blueprint $table) {
                $table->dropIndex('idx_line_users_composite_kpi');
                $table->dropIndex('idx_line_users_dates_kpi');
            });
        }

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_active_status');
            $table->dropIndex('idx_users_status');
        });

        Schema::table('kpi_percents', function (Blueprint $table) {
            $table->dropIndex('idx_kpi_percents_ratio');
            $table->dropIndex('idx_kpi_percents_range');
            $table->dropIndex('idx_kpi_percents_value');
        });

        Schema::table('kpi_ratios', function (Blueprint $table) {
            $table->dropIndex('idx_kpi_ratios_composite');
            $table->dropIndex('idx_kpi_ratios_line_kpi');
            $table->dropIndex('idx_kpi_ratios_polymorphic');
            $table->dropIndex('idx_kpi_ratios_kpi');
        });

        Schema::table('kpis', function (Blueprint $table) {
            $table->dropIndex('idx_kpis_name_deleted');
            $table->dropIndex('idx_kpis_deleted');
        });

        Schema::table('post_visit_kpis', function (Blueprint $table) {
            $table->dropIndex('idx_post_kpis_user_kpi_date');
            $table->dropIndex('idx_post_kpis_date_user');
            $table->dropIndex('idx_post_kpis_kpi_user');
            $table->dropIndex('idx_post_kpis_line_date');
            $table->dropIndex('idx_post_kpis_percent_kpi');
        });
    }
};
